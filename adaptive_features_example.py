"""
自适应下采样和可调渐变色功能使用示例
Adaptive Downsampling and Adjustable Colormap Usage Example

这个示例展示了如何使用新增的自适应下采样和可调渐变色功能
This example demonstrates how to use the new adaptive downsampling and adjustable colormap features
"""

from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)

def example_adaptive_downsampling():
    """自适应下采样使用示例"""
    
    # =============================================================================
    # 自适应下采样配置示例
    # Adaptive Downsampling Configuration Examples
    # =============================================================================
    
    JSON_FILE_PATH = "your_battery_data.json"  # 替换为您的数据文件
    SAMPLE_NAME = "Adaptive_Downsampling_Example"
    KCLS = [1, 10, 25]
    
    # 示例1: 传统统一下采样
    # Example 1: Traditional uniform downsampling
    traditional_config = 5  # 所有数据点统一使用5倍下采样
    
    # 示例2: 自适应下采样 - 保护高电流区域细节
    # Example 2: Adaptive downsampling - preserve high current region details
    adaptive_config_conservative = {
        'i_threshold': 0.5,    # 0.5A作为阈值
        'factor_high': 2,      # 高电流区域轻度下采样
        'factor_low': 8        # 低电流区域重度下采样
    }
    
    # 示例3: 自适应下采样 - 平衡模式
    # Example 3: Adaptive downsampling - balanced mode
    adaptive_config_balanced = {
        'i_threshold': 1.0,    # 1.0A作为阈值
        'factor_high': 3,      # 高电流区域中度下采样
        'factor_low': 6        # 低电流区域重度下采样
    }
    
    configs = [
        ("Traditional", traditional_config),
        ("Adaptive_Conservative", adaptive_config_conservative),
        ("Adaptive_Balanced", adaptive_config_balanced)
    ]
    
    try:
        log("="*60)
        log("自适应下采样示例 / Adaptive Downsampling Examples")
        log("="*60)
        
        # 提取数据
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(JSON_FILE_PATH)
        if all_cycles_data is None:
            log("错误: 无法提取数据 / Error: Failed to extract data")
            return
        
        for config_name, downsample_params in configs:
            log(f"处理配置: {config_name}")
            
            # 处理dI/dt数据
            didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_params=downsample_params)
            
            # 检测膝点
            knee_points = detect_knee_points(cycle_capacities)
            
            # 创建图表
            fig = create_battery_analysis_plots(
                all_cycles_data=all_cycles_data,
                didt_cycles_data=didt_cycles_data,
                cycle_capacities=cycle_capacities,
                cycle_ce_values=cycle_ce_values,
                knee_points=knee_points,
                Kcls=KCLS,
                sample_name=f"{SAMPLE_NAME}_{config_name}",
                save_path=f"{SAMPLE_NAME}_{config_name}.png",
                colormap="viridis"
            )
            
            log(f"✓ {config_name} 配置处理完成")
        
        log("自适应下采样示例完成!")
        
    except Exception as e:
        log(f"错误: {str(e)}")

def example_colormap_options():
    """渐变色选项使用示例"""
    
    JSON_FILE_PATH = "your_battery_data.json"
    SAMPLE_NAME = "Colormap_Example"
    KCLS = [5, 15, 30]
    
    # 不同的颜色映射选项
    colormap_options = [
        ("viridis", "科学可视化标准，从紫色到黄色"),
        ("plasma", "高对比度，从紫色到粉红色到黄色"),
        ("coolwarm", "冷暖对比，从蓝色到红色"),
        ("jet", "经典彩虹色谱，从蓝色到红色"),
        ("rainbow", "完整彩虹色谱"),
        ("tab10", "分类色谱，适合少量循环")
    ]
    
    try:
        log("="*60)
        log("渐变色选项示例 / Colormap Options Examples")
        log("="*60)
        
        # 提取数据
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(JSON_FILE_PATH)
        if all_cycles_data is None:
            return
        
        # 使用适中的下采样
        didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_params=3)
        knee_points = detect_knee_points(cycle_capacities)
        
        for colormap, description in colormap_options:
            log(f"应用颜色映射: {colormap} - {description}")
            
            fig = create_battery_analysis_plots(
                all_cycles_data=all_cycles_data,
                didt_cycles_data=didt_cycles_data,
                cycle_capacities=cycle_capacities,
                cycle_ce_values=cycle_ce_values,
                knee_points=knee_points,
                Kcls=KCLS,
                sample_name=f"{SAMPLE_NAME}_{colormap}",
                save_path=f"{SAMPLE_NAME}_{colormap}.png",
                colormap=colormap
            )
            
            log(f"✓ {colormap} 颜色映射应用完成")
        
        log("渐变色选项示例完成!")
        
    except Exception as e:
        log(f"错误: {str(e)}")

def example_combined_features():
    """组合功能使用示例"""
    
    JSON_FILE_PATH = "your_battery_data.json"
    SAMPLE_NAME = "Combined_Features_Example"
    
    # 组合使用自适应下采样和自定义颜色映射
    downsample_config = {
        'i_threshold': 0.8,
        'factor_high': 2,
        'factor_low': 6
    }
    
    colormap = "plasma"  # 使用plasma颜色映射
    
    try:
        log("="*60)
        log("组合功能示例 / Combined Features Example")
        log("="*60)
        
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(JSON_FILE_PATH)
        if all_cycles_data is None:
            return
        
        didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_params=downsample_config)
        knee_points = detect_knee_points(cycle_capacities)
        
        fig = create_battery_analysis_plots(
            all_cycles_data=all_cycles_data,
            didt_cycles_data=didt_cycles_data,
            cycle_capacities=cycle_capacities,
            cycle_ce_values=cycle_ce_values,
            knee_points=knee_points,
            Kcls=[1, 10, 20, 50],
            sample_name=SAMPLE_NAME,
            save_path=f"{SAMPLE_NAME}.png",
            colormap=colormap,
            # 同时使用自定义Y轴范围
            current_ylim=(-2.5, 0.5),
            didt_ylim=(-0.08, 0.02),
            capacity_ylim=(0.8, 1.3),
            ce_ylim=(0.98, 1.01),
            change_rate_ylim=(-0.06, 0.01)
        )
        
        log("✓ 组合功能示例完成!")
        log(f"应用的配置:")
        log(f"  - 自适应下采样: 阈值{downsample_config['i_threshold']}A")
        log(f"  - 颜色映射: {colormap}")
        log(f"  - 自定义Y轴范围: 已应用")
        
    except Exception as e:
        log(f"错误: {str(e)}")

def main():
    """主函数"""
    print("自适应功能使用示例 / Adaptive Features Usage Examples")
    print("="*60)
    
    print("可用功能 / Available Features:")
    print("1. 自适应下采样 / Adaptive Downsampling")
    print("   - 基于电流阈值的不同下采样因子")
    print("   - 保护重要数据区域的细节")
    print("2. 可调渐变色 / Adjustable Colormap")
    print("   - 多种科学可视化色谱选择")
    print("   - 提高数据可读性和美观度")
    
    print("\n选择示例 / Choose Example:")
    print("1. 自适应下采样示例 / Adaptive downsampling examples")
    print("2. 渐变色选项示例 / Colormap options examples")
    print("3. 组合功能示例 / Combined features example")
    print("4. 全部运行 / Run all examples")
    
    choice = input("\n请输入选择 (1-4) / Enter choice (1-4): ").strip()
    
    if choice == "1":
        example_adaptive_downsampling()
    elif choice == "2":
        example_colormap_options()
    elif choice == "3":
        example_combined_features()
    elif choice == "4":
        example_adaptive_downsampling()
        example_colormap_options()
        example_combined_features()
    else:
        print("无效选择 / Invalid choice")

if __name__ == "__main__":
    main()
