# 模型架构模块使用指南

## 概述

`model_architecture.py` 模块实现了物理增强的Transformer架构，是系统的深度学习核心。该模块通过三点式物理先验嵌入，实现了可解释的析锂风险检测模型。

## 核心设计理念

### 物理先验嵌入三要素
1. **条件Token融合**：将物理软指标编码为条件输入
2. **注意力偏置增强**：基于物理相似性的注意力权重调制  
3. **一致性损失约束**：物理蒸馏确保模型行为符合物理规律

### 架构创新点
- **弱监督学习**：基于物理软标签训练，无需大量人工标注
- **可解释性**：每个预测都有明确的物理解释基础
- **在线推理**：流式设计支持毫秒级实时检测

## 核心类介绍

### 1. PhysicsTransformer 类

主模型类，整合所有物理增强组件。

#### 架构设计
```
输入: [时序数据, 物理特征, 物理信号]
  ↓
[序列编码] ← [物理条件Token] (交叉注意力)
  ↓
[多层Transformer] + [物理注意力偏置]
  ↓
[风险预测头] + [辅助预测头]
  ↓
输出: [风险概率, 物理指标, 解释信息]
```

#### 关键函数说明

```python
def forward(sequence_data: torch.Tensor, physics_features: torch.Tensor,
            physics_signals: Dict[str, torch.Tensor], mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]
```
- **功能**：模型前向传播，整合多模态输入
- **输入**：
  - sequence_data: [batch, seq_len, input_dim] 时序数据
  - physics_features: [batch, physics_dim] 物理特征向量
  - physics_signals: 物理信号字典(曲率、平坦度等)
- **输出**：包含风险预测、物理指标、注意力权重的字典
- **特点**：端到端可微，支持物理约束训练

```python
def encode_sequence(x: torch.Tensor, physics_bias: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor
```
- **功能**：序列编码，集成物理偏置注意力
- **输入**：输入序列、物理偏置矩阵、掩码
- **输出**：物理增强的序列表示
- **核心**：多层Transformer + 物理注意力偏置

### 2. PhysicsAttentionBias 类

物理注意力偏置生成器，实现物理先验的结构化注入。

#### 设计原理
基于物理相似性构造注意力偏置矩阵：
$$B_{ij} = \beta_1 p(i)p(j) + \beta_2 \exp\left(-\frac{(\kappa_i - \kappa_j)^2}{2\sigma_\kappa^2}\right)$$

其中：
- $p(i)$: 时刻i的平坦度分数
- $\kappa_i$: 时刻i的曲率值
- $\beta_1, \beta_2$: 可学习权重

#### 关键函数说明

```python
def compute_physics_bias(curvatures: torch.Tensor, flatness: torch.Tensor) -> torch.Tensor
```
- **功能**：计算物理偏置矩阵
- **输入**：曲率序列、平坦度序列
- **输出**：注意力偏置矩阵 [batch, seq_len, seq_len]
- **物理意义**：相似物理状态的时间点获得更高注意力权重

### 3. MultiHeadPhysicsAttention 类

物理增强的多头注意力机制。

#### 增强方式
在标准注意力计算中加入物理偏置：
$$\text{Attention} = \text{softmax}\left(\frac{QK^T}{\sqrt{d}} + B\right)V$$

#### 关键函数说明

```python
def forward(query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
            physics_bias: Optional[torch.Tensor] = None, mask: Optional[torch.Tensor] = None) -> torch.Tensor
```
- **功能**：物理增强的注意力计算
- **输入**：标准QKV + 物理偏置矩阵
- **输出**：物理感知的注意力输出
- **优势**：保持Transformer架构通用性，仅在需要时启用物理增强

### 4. CrossAttentionFusion 类

条件Token交叉注意力融合模块。

#### 设计目标
将物理条件信息与时序特征进行有效融合，提升模型对物理显著事件的敏感性。

#### 关键函数说明

```python
def forward(sequence_features: torch.Tensor, physics_conditions: torch.Tensor) -> torch.Tensor
```
- **功能**：序列特征与物理条件的交叉注意力融合
- **输入**：
  - sequence_features: [batch, seq_len, d_model] 序列特征
  - physics_conditions: [batch, 1, d_model] 物理条件Token
- **输出**：融合后的增强特征表示
- **机制**：物理条件作为Query，序列特征作为Key和Value

### 5. PhysicsConsistencyLoss 类

物理一致性损失函数，确保模型预测符合物理规律。

#### 损失组成
$$\mathcal{L} = \mathcal{L}_{cls} + \lambda_1\mathcal{L}_{phys} + \lambda_2\mathcal{L}_{aux}$$

#### 关键函数说明

```python
def physics_distillation_loss(y_pred: torch.Tensor, y_physics: torch.Tensor) -> torch.Tensor
```
- **功能**：物理蒸馏损失，确保模型预测与物理软标签一致
- **输入**：模型预测、物理软标签
- **输出**：蒸馏损失值
- **公式**：$\mathcal{L}_{phys} = \text{BCE}(y_{pred}, y_{physics})$

```python
def correlation_loss(predictions: torch.Tensor, physics_scores: torch.Tensor) -> torch.Tensor
```
- **功能**：相关性损失，最大化预测与物理评分的相关性
- **输入**：模型预测、物理评分
- **输出**：相关性损失
- **目标**：$\max \text{corr}(y_{logit}, S_{plat} + \lambda S_{bump})$

### 6. OnlineDetector 类

在线检测器，实现实时流式推理。

#### 设计特点
- **滑窗机制**：固定窗口大小，重叠滑动
- **CV触发**：仅在恒压阶段启动检测
- **低延迟**：边采边算，亚秒级响应

#### 关键函数说明

```python
def sliding_window_inference(data_stream: torch.Tensor) -> List[Dict[str, float]]
```
- **功能**：滑窗实时推理
- **输入**：数据流张量
- **输出**：逐窗口检测结果列表
- **性能**：单窗口推理 < 100ms

```python
def generate_explanation(model_output: Dict[str, torch.Tensor]) -> Dict[str, any]
```
- **功能**：生成检测结果的物理解释
- **输入**：模型输出字典
- **输出**：包含主要指标、置信度、建议的解释字典
- **价值**：提供可理解的诊断信息

## 配置结构

### ModelConfig 数据结构
```python
@dataclass
class ModelConfig:
    d_model: int = 256          # 模型隐藏维度
    n_heads: int = 8            # 注意力头数
    n_layers: int = 6           # Transformer层数
    d_ff: int = 1024           # 前馈网络维度
    dropout: float = 0.1        # Dropout比率
    max_seq_len: int = 512      # 最大序列长度
    physics_dim: int = 7        # 物理特征维度
    n_classes: int = 2          # 分类类别数
```

## 使用示例

### 模型初始化与训练
```python
from model_architecture import PhysicsTransformer, ModelConfig, PhysicsConsistencyLoss

# 配置模型
config = ModelConfig(
    d_model=256,
    n_heads=8,
    n_layers=6,
    physics_dim=7
)

# 初始化模型
model = PhysicsTransformer(config)

# 损失函数
loss_fn = PhysicsConsistencyLoss(weights={
    'physics': 0.5,
    'correlation': 0.3,
    'auxiliary': 0.2
})

# 训练步骤
optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)

for epoch in range(num_epochs):
    for batch in train_loader:
        # 前向传播
        outputs = model(
            sequence_data=batch['sequence'],
            physics_features=batch['physics'],
            physics_signals=batch['signals']
        )
        
        # 计算损失
        losses = loss_fn(
            model_outputs=outputs,
            physics_targets=batch['physics_targets'],
            true_labels=batch['labels']
        )
        
        total_loss = losses['total']
        
        # 反向传播
        optimizer.zero_grad()
        total_loss.backward()
        optimizer.step()
```

### 在线检测部署
```python
from model_architecture import OnlineDetector

# 加载训练好的模型
model = torch.load('trained_model.pth')
model.eval()

# 初始化在线检测器
detector = OnlineDetector(
    model=model,
    window_size=15,  # 15秒窗口
    step_size=1      # 1秒步长
)

# 实时推理
while True:
    # 获取新数据
    new_data = get_realtime_data()
    
    # 检查CV触发
    if detector.check_cv_trigger(new_data['V']):
        # 执行检测
        results = detector.sliding_window_inference(new_data)
        
        for result in results:
            if result['risk_score'] > 0.8:
                # 生成解释
                explanation = detector.generate_explanation(result)
                
                # 发送告警
                send_alert(result, explanation)
```

### 模型量化优化
```python
from model_architecture import ModelOptimizer

optimizer = ModelOptimizer()

# 量化优化
quantized_model = optimizer.quantize_model(
    model=trained_model,
    quantization_config={
        'method': 'dynamic',
        'dtype': torch.qint8,
        'preserve_physics_bias': True  # 保持物理偏置精度
    }
)

# 推理优化
optimized_model = optimizer.optimize_for_inference(quantized_model)

# 导出ONNX
example_input = torch.randn(1, 512, 4)  # batch_size=1, seq_len=512, features=4
optimizer.convert_to_onnx(
    model=optimized_model,
    example_input=example_input,
    output_path='lithium_detector.onnx'
)
```

## 训练策略

### 渐进式训练
```python
# 阶段1: 物理软指标预训练
for epoch in range(pretrain_epochs):
    # 仅使用物理损失训练
    loss = physics_loss_weight * physics_loss
    
# 阶段2: 端到端联合优化  
for epoch in range(joint_epochs):
    # 多目标损失联合训练
    loss = cls_loss + physics_loss + aux_loss
    
# 阶段3: 在线持续学习
for batch in online_stream:
    # 基于反馈的在线更新
    online_update(model, feedback_data)
```

### 损失权重调度
```python
def get_loss_weights(epoch, total_epochs):
    # 物理损失权重随训练衰减
    physics_weight = 1.0 * (1 - epoch / total_epochs) + 0.3
    # 分类损失权重逐渐增加
    cls_weight = 0.5 + 0.5 * (epoch / total_epochs)
    
    return {
        'physics': physics_weight,
        'classification': cls_weight,
        'auxiliary': 0.2
    }
```

## 性能优化

### 计算优化
1. **线性注意力**：使用Performer等线性注意力机制
2. **量化部署**：INT8量化，保持物理偏置FP16精度
3. **图优化**：ONNX Runtime推理加速

### 内存优化
1. **梯度累积**：处理大batch size
2. **混合精度**：FP16训练，关键部分FP32
3. **内存映射**：大数据集的高效加载

### 推理优化
1. **模型剪枝**：移除不重要的连接
2. **知识蒸馏**：压缩为轻量级模型
3. **缓存机制**：重用计算结果

## 故障诊断

### 常见问题
1. **物理损失不收敛**：检查物理目标标签质量
2. **注意力权重异常**：验证物理偏置计算逻辑
3. **推理延迟过高**：考虑模型量化或架构简化

### 调试工具
```python
# 可视化注意力权重
def visualize_attention(model, input_data):
    with torch.no_grad():
        outputs = model(input_data, return_attention=True)
        attention_weights = outputs['attention_weights']
        # 绘制热力图
        plot_attention_heatmap(attention_weights)

# 监控物理一致性
def check_physics_consistency(predictions, physics_scores):
    correlation = torch.corrcoef(torch.stack([predictions, physics_scores]))[0,1]
    print(f"物理一致性相关系数: {correlation:.3f}")
```

## 扩展开发

### 新增物理约束
1. 继承PhysicsConsistencyLoss基类
2. 实现新的物理损失函数
3. 在训练循环中集成

### 自定义注意力机制
1. 继承MultiHeadPhysicsAttention
2. 重写attention计算逻辑  
3. 保持接口兼容性

### 模型变体开发
1. 修改PhysicsTransformer架构
2. 调整物理嵌入策略
3. 验证性能改进

该架构设计充分体现了物理知识与深度学习的深度融合，在保持模型强大学习能力的同时，确保了预测结果的物理可解释性和工程可靠性。
