"""
基于物理先验的Transformer析锂检测系统

这是一个集成了电化学物理机理和深度学习技术的电池析锂检测系统，
提供从数据加载到在线检测的完整解决方案。

主要功能模块：
- data_loader: 数据读取和预处理
- data_processor: CV段检测和物理指标计算  
- model_architecture: 物理增强Transformer模型
- result_output: 结果格式化和报告生成
- visualization: 数据可视化和交互界面

作者: 项目团队
版本: 2.0
许可: MIT License
"""

__version__ = "2.0.0"
__author__ = "LP Detection Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# 导入核心类和函数
from .data_loader import BatteryDataLoader, DataValidator, DataCache
from .data_processor import (
    CVSegmentDetector, 
    PhysicsCalculator, 
    FeatureAggregator, 
    AutoLabeler,
    CVSegment,
    PhysicsMetrics
)
from .model_architecture import (
    PhysicsTransformer,
    OnlineDetector,
    ModelConfig,
    PhysicsConsistencyLoss,
    ModelTrainer
)
from .result_output import (
    ResultFormatter,
    ReportGenerator,
    DataExporter,
    AlertManager,
    DetectionResult,
    BatchResult
)
from .visualization import (
    DataVisualizer,
    ModelVisualizer,
    InteractiveVisualizer,
    StatisticalVisualizer,
    ExplanationVisualizer
)

# 便捷导入别名
BatteryLoader = BatteryDataLoader
CVDetector = CVSegmentDetector
PhysicsModel = PhysicsTransformer
Visualizer = DataVisualizer

# 模块信息
__all__ = [
    # 数据模块
    'BatteryDataLoader', 'DataValidator', 'DataCache', 'BatteryLoader',
    
    # 处理模块
    'CVSegmentDetector', 'PhysicsCalculator', 'FeatureAggregator', 'AutoLabeler',
    'CVDetector', 'CVSegment', 'PhysicsMetrics',
    
    # 模型模块  
    'PhysicsTransformer', 'OnlineDetector', 'ModelConfig', 
    'PhysicsConsistencyLoss', 'ModelTrainer', 'PhysicsModel',
    
    # 输出模块
    'ResultFormatter', 'ReportGenerator', 'DataExporter', 'AlertManager',
    'DetectionResult', 'BatchResult',
    
    # 可视化模块
    'DataVisualizer', 'ModelVisualizer', 'InteractiveVisualizer',
    'StatisticalVisualizer', 'ExplanationVisualizer', 'Visualizer'
]

# 版本检查
def check_dependencies():
    """检查依赖包版本"""
    import sys
    
    # 最低Python版本要求
    if sys.version_info < (3, 8):
        raise RuntimeError("需要Python 3.8或更高版本")
    
    # 检查关键依赖
    try:
        import torch
        import numpy as np
        import pandas as pd
        import matplotlib
        import sklearn
        
        print(f"✅ 依赖检查通过")
        print(f"   Python: {sys.version}")
        print(f"   PyTorch: {torch.__version__}")
        print(f"   NumPy: {np.__version__}")
        print(f"   Pandas: {pd.__version__}")
        
    except ImportError as e:
        raise ImportError(f"缺少必要依赖: {e}")

# 配置信息
DEFAULT_CONFIG = {
    'data_loader': {
        'sampling_rate': 1.0,
        'cache_size': 1000,
        'validation_enabled': True
    },
    'cv_detector': {
        'v_var_th': 1e-4,
        'i_slope_th': -1e-5,
        'min_duration': 30.0,
        'adaptive_enabled': True
    },
    'physics_calculator': {
        'sg_win': 11,
        'tau_slope': 0.002,
        'alpha_base': 0.98,
        'betas': [1.0, 1.0, 0.5, 1.0, 1.0, 0.5, 0.5]
    },
    'model': {
        'd_model': 256,
        'n_heads': 8,
        'n_layers': 6,
        'physics_weight': 0.5,
        'window_size': 15
    },
    'output': {
        'format': 'json',
        'alert_enabled': True,
        'report_auto_generate': True
    },
    'visualization': {
        'style': 'seaborn',
        'dpi': 300,
        'interactive': True
    }
}

def get_default_config():
    """获取默认配置"""
    return DEFAULT_CONFIG.copy()

def print_system_info():
    """打印系统信息"""
    import platform
    
    print("=" * 60)
    print("🔋 基于物理先验的Transformer析锂检测系统")
    print("=" * 60)
    print(f"版本: {__version__}")
    print(f"作者: {__author__}")
    print(f"许可: {__license__}")
    print("-" * 60)
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {platform.python_version()}")
    print(f"架构: {platform.machine()}")
    print("=" * 60)

# 初始化时的自动检查
if __name__ != "__main__":
    try:
        check_dependencies()
    except Exception as e:
        import warnings
        warnings.warn(f"依赖检查失败: {e}", ImportWarning)
