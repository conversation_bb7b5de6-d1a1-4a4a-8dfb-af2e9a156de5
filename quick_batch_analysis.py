"""
快速批量电池数据分析脚本
Quick Batch Battery Data Analysis Script

简化版本，直接输入路径即可批量处理
Simplified version for direct path input and batch processing
"""

import os
import glob
import sys
from pathlib import Path
from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)

def extract_sample_name(json_file_path):
    """从文件路径提取样品名称"""
    filename = Path(json_file_path).stem
    
    # 移除常见后缀
    suffixes = ['_CV_Qcha_CE', '_data', '_battery', '_cycle', '_test']
    sample_name = filename
    
    for suffix in suffixes:
        if sample_name.endswith(suffix):
            sample_name = sample_name[:-len(suffix)]
    
    return sample_name

def quick_process_file(json_file_path, output_dir):
    """快速处理单个文件 - 使用当前配置"""
    try:
        sample_name = extract_sample_name(json_file_path)
        output_path = os.path.join(output_dir, f"{sample_name}_analysis.png")
        
        log(f"处理: {sample_name}")
        
        # 使用当前的配置参数（与您的设置一致）
        CONFIG = {
            'kcls': [2, 20, 50, 150],
            'downsample_params': {
                'i_threshold': 1,
                'factor_high': 5,
                'factor_low': 3
            },
            'colormap': 'Blues',
            'figsize': (15, 12),
            'dpi': 330,
            'current_ylim': (0, 2),
            'didt_ylim': (-0.006, 0.001),
            'capacity_ylim': None,
            'ce_ylim': (0.98, 1.01),
            'change_rate_ylim': None,
        }
        
        # 数据处理
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(json_file_path)
        if all_cycles_data is None:
            log(f"  ✗ 数据提取失败")
            return False
        
        didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_params=CONFIG['downsample_params'])
        knee_points = detect_knee_points(cycle_capacities)
        
        # 创建图表
        fig = create_battery_analysis_plots(
            all_cycles_data=all_cycles_data,
            didt_cycles_data=didt_cycles_data,
            cycle_capacities=cycle_capacities,
            cycle_ce_values=cycle_ce_values,
            knee_points=knee_points,
            Kcls=CONFIG['kcls'],
            sample_name=sample_name,
            figsize=CONFIG['figsize'],
            dpi=CONFIG['dpi'],
            save_path=output_path,
            current_ylim=CONFIG['current_ylim'],
            didt_ylim=CONFIG['didt_ylim'],
            capacity_ylim=CONFIG['capacity_ylim'],
            ce_ylim=CONFIG['ce_ylim'],
            change_rate_ylim=CONFIG['change_rate_ylim'],
            colormap=CONFIG['colormap'],
        )
        
        log(f"  ✓ 完成 - 循环数: {len(all_cycles_data)}, 膝点: {knee_points}")
        return True
        
    except Exception as e:
        log(f"  ✗ 错误: {str(e)}")
        return False

def quick_batch_process(input_path):
    """快速批量处理"""
    
    # 验证路径
    if not os.path.exists(input_path):
        print(f"错误: 路径 '{input_path}' 不存在")
        return
    
    # 获取JSON文件列表
    if os.path.isfile(input_path) and input_path.endswith('.json'):
        json_files = [input_path]
        output_dir = os.path.dirname(input_path)
    elif os.path.isdir(input_path):
        json_files = glob.glob(os.path.join(input_path, "*.json"))
        output_dir = input_path
    else:
        print(f"错误: '{input_path}' 不是有效的JSON文件或目录")
        return
    
    if not json_files:
        print(f"在 '{input_path}' 中未找到JSON文件")
        return
    
    print(f"找到 {len(json_files)} 个JSON文件")
    print(f"输出目录: {output_dir}")
    print("="*60)
    
    # 批量处理
    successful = 0
    failed = 0
    
    for i, json_file in enumerate(json_files, 1):
        print(f"[{i}/{len(json_files)}] ", end="")
        
        if quick_process_file(json_file, output_dir):
            successful += 1
        else:
            failed += 1
    
    # 结果总结
    print("="*60)
    print(f"批量处理完成!")
    print(f"成功: {successful}, 失败: {failed}, 总计: {len(json_files)}")
    print(f"成功率: {successful/len(json_files)*100:.1f}%")
    print(f"图片保存在: {output_dir}")

def main():
    """主函数"""
    print("快速批量电池数据分析工具")
    print("Quick Batch Battery Data Analysis Tool")
    print("="*60)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        input_path = sys.argv[1]
        print(f"使用命令行参数: {input_path}")
    else:
        # 交互式输入
        input_path = input("请输入JSON文件路径或目录路径: ").strip().strip('"')
    
    if not input_path:
        print("错误: 未提供路径")
        return
    
    # 显示当前配置
    print(f"\n当前配置:")
    print(f"  - 标记循环: [2, 20, 50, 150]")
    print(f"  - 颜色映射: Blues")
    print(f"  - 图片DPI: 330")
    print(f"  - 电流阈值: 1A (高电流因子:5, 低电流因子:3)")
    print(f"  - 电流Y轴: (0, 2)")
    print(f"  - dI/dt Y轴: (-0.006, 0.001)")
    print(f"  - CE Y轴: (0.98, 1.01)")
    
    confirm = input(f"\n开始处理? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes', '是', '']:
        print("已取消")
        return
    
    # 开始处理
    quick_batch_process(input_path)

if __name__ == "__main__":
    main()
