"""
测试新增的Y轴范围功能
Test the new Y-axis range functionality
"""

from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)
import json
import numpy as np
import pandas as pd

def create_test_data():
    """创建测试数据"""
    log("Creating test data for Y-axis range testing...")
    
    # 生成测试数据
    test_data = {}
    
    # 生成8个循环的示例数据
    for cycle in range(1, 9):
        # 生成时间数据 (分钟)
        time_points = 60
        time_min = np.linspace(0, 1.5, time_points)  # 1.5分钟的测试时间
        
        # 生成电流数据 (模拟放电曲线)
        current_A = []
        for t in time_min:
            if t < 0.1:  # 初始阶段
                current = -1.5
            elif t < 1.2:  # 主要放电阶段
                current = -1.5 + 0.2 * np.sin(t * 15) * np.exp(-t/0.8)
            else:  # 结束阶段
                current = -0.2 * (1.5 - t)
            
            # 添加一些噪声和循环差异
            current += np.random.normal(0, 0.02)
            current *= (1 - 0.02 * (cycle - 1))  # 模拟性能衰减
            current_A.append(current)
        
        # 存储循环数据
        test_data[f"Cycle_{cycle}"] = {
            "relative_time_min": time_min.tolist(),
            "current_A": current_A
        }
    
    # 生成容量数据 (模拟容量衰减)
    capacities = []
    for cycle in range(1, 9):
        capacity = 1.2 - 0.03 * (cycle - 1) - 0.005 * (cycle - 1)**2  # 模拟衰减
        capacities.append(capacity)
    
    test_data["Discharge Capacity"] = capacities
    
    # 生成库伦效率数据
    ce_values = []
    for cycle in range(1, 9):
        ce = 0.995 - 0.002 * (cycle - 1) + 0.003 * np.random.random()  # 模拟CE值
        ce_values.append(ce)
    
    test_data["CE"] = ce_values
    
    # 保存到文件
    filename = "test_ylim_data.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2)
    
    log(f"Test data file created: {filename}")
    return filename

def test_ylim_functionality():
    """测试Y轴范围功能"""
    
    # 创建测试数据
    test_file = create_test_data()
    
    try:
        log("="*60)
        log("Testing Y-axis range functionality...")
        log("="*60)
        
        # 步骤1: 提取数据
        log("Step 1: Extracting data...")
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(test_file)
        
        if all_cycles_data is None:
            log("Error: Failed to extract data")
            return False
        
        # 步骤2: 计算dI/dt
        log("Step 2: Calculating dI/dt...")
        didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_factor=1)
        
        # 步骤3: 检测膝点
        log("Step 3: Detecting knee points...")
        knee_points = detect_knee_points(cycle_capacities)
        
        # 步骤4: 测试不同的Y轴范围设置
        test_cases = [
            {
                "name": "Default_Auto_Range",
                "current_ylim": None,
                "didt_ylim": None,
                "capacity_ylim": None,
                "ce_ylim": None,
                "change_rate_ylim": None,
                "save_path": "test_auto_range.png"
            },
            {
                "name": "Custom_All_Ranges",
                "current_ylim": (-2.0, 0.5),
                "didt_ylim": (-0.05, 0.01),
                "capacity_ylim": (0.8, 1.3),
                "ce_ylim": (0.99, 1.00),
                "change_rate_ylim": (-0.08, 0.02),
                "save_path": "test_custom_all.png"
            },
            {
                "name": "Partial_Custom_Ranges",
                "current_ylim": (-1.8, 0.2),
                "didt_ylim": None,  # Auto
                "capacity_ylim": (0.9, 1.25),
                "ce_ylim": None,  # Auto
                "change_rate_ylim": (-0.06, 0.01),
                "save_path": "test_partial_custom.png"
            },
            {
                "name": "Tight_Ranges",
                "current_ylim": (-1.6, -0.1),
                "didt_ylim": (-0.02, 0.005),
                "capacity_ylim": (1.0, 1.2),
                "ce_ylim": (0.992, 0.998),
                "change_rate_ylim": (-0.04, 0.005),
                "save_path": "test_tight_ranges.png"
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            log(f"Step 4.{i+1}: Testing {test_case['name']}...")
            
            fig = create_battery_analysis_plots(
                all_cycles_data=all_cycles_data,
                didt_cycles_data=didt_cycles_data,
                cycle_capacities=cycle_capacities,
                cycle_ce_values=cycle_ce_values,
                knee_points=knee_points,
                Kcls=[1, 3, 5, 7],  # Mark some cycles
                sample_name=f"YLim_Test_{test_case['name']}",
                figsize=(15, 12),
                dpi=100,
                save_path=test_case['save_path'],
                current_ylim=test_case['current_ylim'],
                didt_ylim=test_case['didt_ylim'],
                capacity_ylim=test_case['capacity_ylim'],
                ce_ylim=test_case['ce_ylim'],
                change_rate_ylim=test_case['change_rate_ylim'],
            )
            
            log(f"  ✓ {test_case['name']} completed successfully")
            log(f"    - Current Y-lim: {test_case['current_ylim']}")
            log(f"    - dI/dt Y-lim: {test_case['didt_ylim']}")
            log(f"    - Capacity Y-lim: {test_case['capacity_ylim']}")
            log(f"    - CE Y-lim: {test_case['ce_ylim']}")
            log(f"    - Change rate Y-lim: {test_case['change_rate_ylim']}")
        
        # 输出测试总结
        log("="*60)
        log("Y-axis range functionality test completed successfully!")
        log("="*60)
        log("Test Summary:")
        log(f"  - Total cycles processed: {len(all_cycles_data)}")
        log(f"  - Cycles with dI/dt data: {len(didt_cycles_data)}")
        log(f"  - Cycles with capacity data: {len(cycle_capacities)}")
        log(f"  - Cycles with CE data: {len(cycle_ce_values) if cycle_ce_values else 0}")
        log(f"  - Auto-detected knee points: {knee_points}")
        log(f"  - Test cases completed: {len(test_cases)}")
        log("="*60)
        
        # 验证新功能
        log("Verification of Y-axis range features:")
        log("  ✓ Current plot Y-axis range customization")
        log("  ✓ dI/dt plot Y-axis range customization")
        log("  ✓ Capacity plot left Y-axis range customization")
        log("  ✓ CE plot right Y-axis range customization")
        log("  ✓ Capacity change rate plot Y-axis range customization")
        log("  ✓ Mixed auto/custom range settings")
        log("  ✓ Marker positioning adapts to Y-axis ranges")
        
        return True
        
    except Exception as e:
        log(f"Error during testing: {str(e)}")
        import traceback
        log(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("Y-axis Range Functionality Test Suite")
    print("="*60)
    
    success = test_ylim_functionality()
    
    if success:
        print("\n✓ All Y-axis range tests passed successfully!")
        print("Check the generated PNG files to verify the Y-axis customization:")
        print("  - test_auto_range.png (all auto ranges)")
        print("  - test_custom_all.png (all custom ranges)")
        print("  - test_partial_custom.png (mixed auto/custom)")
        print("  - test_tight_ranges.png (tight custom ranges)")
        print("\nNew Y-axis parameters available:")
        print("  - capacity_ylim: Controls capacity plot left Y-axis")
        print("  - ce_ylim: Controls CE plot right Y-axis")
        print("  - change_rate_ylim: Controls capacity change rate plot Y-axis")
    else:
        print("\n✗ Tests failed. Check the log for details.")

if __name__ == "__main__":
    main()
