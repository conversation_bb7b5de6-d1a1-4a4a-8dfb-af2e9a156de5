"""
测试自适应下采样和可调渐变色功能
Test adaptive downsampling and adjustable colormap functionality
"""

from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)
import json
import numpy as np
import pandas as pd

def create_test_data_with_varying_current():
    """创建包含不同电流大小的测试数据"""
    log("Creating test data with varying current magnitudes...")
    
    # 生成测试数据
    test_data = {}
    
    # 生成6个循环的示例数据，包含不同的电流模式
    for cycle in range(1, 7):
        # 生成时间数据 (分钟)
        time_points = 200  # 更多数据点以测试下采样效果
        time_min = np.linspace(0, 2, time_points)  # 2分钟的测试时间
        
        # 生成电流数据 (模拟不同的放电模式)
        current_A = []
        for t in time_min:
            if t < 0.2:  # 初始高电流阶段
                current = -2.0 + 0.1 * np.sin(t * 50)
            elif t < 0.4:  # 过渡阶段
                current = -2.0 + (t - 0.2) * 5  # 从-2A逐渐减小
            elif t < 1.5:  # 主要放电阶段 - 低电流
                current = -0.3 + 0.05 * np.sin(t * 20) * np.exp(-t/1.0)
            else:  # 结束阶段 - 极低电流
                current = -0.1 * (2 - t)
            
            # 添加一些噪声和循环差异
            current += np.random.normal(0, 0.02)
            current *= (1 - 0.01 * (cycle - 1))  # 模拟性能衰减
            current_A.append(current)
        
        # 存储循环数据
        test_data[f"Cycle_{cycle}"] = {
            "relative_time_min": time_min.tolist(),
            "current_A": current_A
        }
    
    # 生成容量数据
    capacities = []
    for cycle in range(1, 7):
        capacity = 1.5 - 0.04 * (cycle - 1) - 0.008 * (cycle - 1)**2
        capacities.append(capacity)
    
    test_data["Discharge Capacity"] = capacities
    
    # 生成CE数据
    ce_values = []
    for cycle in range(1, 7):
        ce = 0.996 - 0.001 * (cycle - 1) + 0.002 * np.random.random()
        ce_values.append(ce)
    
    test_data["CE"] = ce_values
    
    # 保存到文件
    filename = "test_adaptive_data.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2)
    
    log(f"Test data file created: {filename}")
    return filename

def test_adaptive_downsampling():
    """测试自适应下采样功能"""
    
    # 创建测试数据
    test_file = create_test_data_with_varying_current()
    
    try:
        log("="*60)
        log("Testing adaptive downsampling functionality...")
        log("="*60)
        
        # 提取数据
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(test_file)
        if all_cycles_data is None:
            return False
        
        # 测试不同的下采样配置
        downsample_configs = [
            {
                "name": "Traditional_Uniform",
                "params": 3,
                "description": "Traditional uniform downsampling factor 3"
            },
            {
                "name": "Adaptive_Conservative",
                "params": {
                    'i_threshold': 0.5,
                    'factor_high': 2,
                    'factor_low': 4
                },
                "description": "Conservative adaptive: high current less downsampled"
            },
            {
                "name": "Adaptive_Aggressive",
                "params": {
                    'i_threshold': 1.0,
                    'factor_high': 3,
                    'factor_low': 8
                },
                "description": "Aggressive adaptive: low current heavily downsampled"
            },
            {
                "name": "Adaptive_Balanced",
                "params": {
                    'i_threshold': 0.8,
                    'factor_high': 2,
                    'factor_low': 6
                },
                "description": "Balanced adaptive downsampling"
            }
        ]
        
        for config in downsample_configs:
            log(f"Testing {config['name']}: {config['description']}")
            
            # 处理dI/dt数据
            didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_params=config['params'])
            
            # 检测膝点
            knee_points = detect_knee_points(cycle_capacities)
            
            # 创建图表
            fig = create_battery_analysis_plots(
                all_cycles_data=all_cycles_data,
                didt_cycles_data=didt_cycles_data,
                cycle_capacities=cycle_capacities,
                cycle_ce_values=cycle_ce_values,
                knee_points=knee_points,
                Kcls=[1, 3, 6],
                sample_name=f"Adaptive_Test_{config['name']}",
                figsize=(15, 12),
                dpi=100,
                save_path=f"test_adaptive_{config['name'].lower()}.png",
                colormap="viridis"
            )
            
            log(f"  ✓ {config['name']} completed successfully")
        
        return True
        
    except Exception as e:
        log(f"Error during adaptive downsampling test: {str(e)}")
        return False

def test_colormap_options():
    """测试不同渐变色选项"""
    
    test_file = create_test_data_with_varying_current()
    
    try:
        log("="*60)
        log("Testing colormap functionality...")
        log("="*60)
        
        # 提取数据
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(test_file)
        if all_cycles_data is None:
            return False
        
        # 处理数据
        didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_params=2)
        knee_points = detect_knee_points(cycle_capacities)
        
        # 测试不同的颜色映射
        colormaps = [
            ("viridis", "科学可视化标准色谱"),
            ("plasma", "高对比度紫红色谱"),
            ("coolwarm", "冷暖色对比色谱"),
            ("jet", "经典彩虹色谱"),
            ("rainbow", "完整彩虹色谱"),
            ("tab10", "分类色谱")
        ]
        
        for colormap, description in colormaps:
            log(f"Testing colormap '{colormap}': {description}")
            
            fig = create_battery_analysis_plots(
                all_cycles_data=all_cycles_data,
                didt_cycles_data=didt_cycles_data,
                cycle_capacities=cycle_capacities,
                cycle_ce_values=cycle_ce_values,
                knee_points=knee_points,
                Kcls=[2, 4],
                sample_name=f"Colormap_Test_{colormap}",
                figsize=(15, 12),
                dpi=100,
                save_path=f"test_colormap_{colormap}.png",
                colormap=colormap
            )
            
            log(f"  ✓ Colormap '{colormap}' applied successfully")
        
        return True
        
    except Exception as e:
        log(f"Error during colormap test: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("Adaptive Features Test Suite")
    print("="*60)
    print("Testing:")
    print("1. Adaptive downsampling based on current threshold")
    print("2. Adjustable colormap for gradient colors")
    print("="*60)
    
    # 测试自适应下采样
    success1 = test_adaptive_downsampling()
    
    # 测试渐变色选项
    success2 = test_colormap_options()
    
    if success1 and success2:
        print("\n✓ All adaptive feature tests passed successfully!")
        print("\nGenerated test files:")
        print("Adaptive downsampling tests:")
        print("  - test_adaptive_traditional_uniform.png")
        print("  - test_adaptive_adaptive_conservative.png")
        print("  - test_adaptive_adaptive_aggressive.png")
        print("  - test_adaptive_adaptive_balanced.png")
        print("\nColormap tests:")
        print("  - test_colormap_viridis.png")
        print("  - test_colormap_plasma.png")
        print("  - test_colormap_coolwarm.png")
        print("  - test_colormap_jet.png")
        print("  - test_colormap_rainbow.png")
        print("  - test_colormap_tab10.png")
        
        print("\nNew features summary:")
        print("✓ Adaptive downsampling: Different factors for high/low current regions")
        print("✓ Adjustable colormap: Multiple color schemes available")
        print("✓ Backward compatibility: Traditional uniform downsampling still supported")
        
    else:
        print("\n✗ Some tests failed. Check the log for details.")

if __name__ == "__main__":
    main()
