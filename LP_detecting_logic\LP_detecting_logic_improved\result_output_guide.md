# 结果输出模块使用指南

## 概述

`result_output.py` 模块负责将模型检测结果转化为可理解、可操作的输出格式，提供完整的结果管理、报告生成和告警服务。该模块是系统与用户交互的最终环节。

## 核心设计理念

### 输出标准化
- **统一格式**：标准化的结果数据结构
- **多级粒度**：单次检测、批量分析、趋势报告
- **可追溯性**：完整的检测过程记录和参数保存

### 可解释性
- **物理解释**：基于物理指标的风险解释
- **置信度评估**：检测结果的可信度量化
- **建议输出**：可操作的应对建议

## 核心类介绍

### 1. ResultFormatter 类

结果格式化器，负责将原始模型输出转换为标准化格式。

#### 设计目标
将模型的tensor输出转化为业务可理解的结构化信息，包含风险评分、物理解释、置信度评估等。

#### 关键函数说明

```python
def format_single_result(raw_output: Dict, metadata: Dict) -> DetectionResult
```
- **功能**：格式化单个检测结果
- **输入**：
  - raw_output: 模型原始输出字典
  - metadata: 样本元数据(电池ID、循环数等)
- **输出**：DetectionResult对象
- **处理流程**：
  1. 提取风险评分并映射为风险等级
  2. 解析物理指标并生成解释
  3. 计算置信度并添加建议信息

```python
def determine_risk_level(risk_score: float, thresholds: Dict[str, float]) -> str
```
- **功能**：基于评分确定风险等级
- **输入**：风险评分、等级阈值字典
- **输出**：风险等级字符串("normal"/"warning"/"critical")
- **逻辑**：
  - normal: risk_score < 0.7
  - warning: 0.7 ≤ risk_score < 0.9  
  - critical: risk_score ≥ 0.9

```python
def generate_explanation(physics_metrics: Dict, model_attention: Optional[np.ndarray] = None) -> Dict[str, any]
```
- **功能**：生成检测结果的物理解释
- **输入**：物理指标字典、模型注意力权重(可选)
- **输出**：解释信息字典
- **内容包含**：
  - 主要风险指标识别
  - 物理现象描述
  - 发生位置和强度
  - 应对建议

### 2. ReportGenerator 类

报告生成器，创建多层次的分析报告。

#### 报告类型
1. **汇总报告**：批次检测的整体统计
2. **详细报告**：单个样本的深入分析
3. **趋势报告**：时间序列的变化趋势
4. **对比报告**：不同条件下的性能对比

#### 关键函数说明

```python
def generate_summary_report(batch_result: BatchResult) -> Dict[str, any]
```
- **功能**：生成批量检测汇总报告
- **输入**：BatchResult对象
- **输出**：汇总报告字典
- **包含内容**：
  - 检测样本总数和高风险比例
  - 物理指标分布统计
  - 检测性能指标
  - 关键发现摘要

```python
def generate_trend_analysis(historical_results: List[BatchResult], time_window: int) -> Dict[str, any]
```
- **功能**：生成趋势分析报告
- **输入**：历史结果列表、时间窗口(天)
- **输出**：趋势分析字典
- **分析维度**：
  - 风险评分时间趋势
  - 物理指标变化规律
  - 检测频率统计
  - 异常事件识别

### 3. DataExporter 类

数据导出器，支持多种格式的结果导出。

#### 支持格式
- **CSV**：表格化数据，便于Excel分析
- **JSON**：结构化数据，便于程序处理
- **Excel**：包含图表的丰富报告
- **实时流**：推送到外部系统

#### 关键函数说明

```python
def export_to_csv(results: List[DetectionResult], filename: str) -> str
```
- **功能**：导出为CSV格式
- **输入**：检测结果列表、文件名
- **输出**：导出文件路径
- **CSV结构**：
  ```csv
  timestamp,cell_id,cycle_id,risk_score,risk_level,S_plat,S_bump,S_amp,confidence
  2024-12-25 10:30:00,Cell_001,1250,0.85,warning,0.78,0.62,0.012,0.92
  ```

```python
def export_to_excel(results: List[DetectionResult], filename: str, include_charts: bool = True) -> str
```
- **功能**：导出为Excel格式，可包含图表
- **输入**：结果列表、文件名、是否包含图表
- **输出**：Excel文件路径
- **特色功能**：
  - 多工作表组织(原始数据、统计分析、图表)
  - 自动生成趋势图和分布图
  - 条件格式化突出显示异常值

### 4. AlertManager 类

告警管理器，实现智能告警和通知服务。

#### 告警策略
1. **实时告警**：高风险检测立即触发
2. **趋势告警**：风险趋势异常时触发
3. **系统告警**：检测系统故障时触发

#### 关键函数说明

```python
def check_alert_conditions(result: DetectionResult) -> List[Dict[str, any]]
```
- **功能**：检查是否满足告警条件
- **输入**：检测结果对象
- **输出**：触发的告警列表
- **告警类型**：
  - 高风险告警：risk_score ≥ 0.9
  - 持续风险告警：连续多次warning级别
  - 异常指标告警：物理指标超出正常范围

```python
def generate_alert_message(result: DetectionResult, alert_type: str) -> Dict[str, any]
```
- **功能**：生成告警消息
- **输入**：检测结果、告警类型
- **输出**：结构化告警消息
- **消息内容**：
  ```json
  {
    "alert_id": "ALT_20241225_103000_001",
    "severity": "critical",
    "title": "检测到严重析锂风险",
    "description": "电池Cell_001在循环1250中出现明显的电流平台现象",
    "risk_score": 0.95,
    "primary_indicators": ["高回嵌幅度", "明显电流平台"],
    "location": "CV阶段后半段",
    "recommendation": "立即停止充电，检查充电参数设置",
    "timestamp": "2024-12-25T10:30:00Z"
  }
  ```

### 5. StatisticsCalculator 类

统计计算器，提供深入的数据分析功能。

#### 统计维度
- **检测性能**：准确率、召回率、F1分数
- **时间趋势**：按小时/天/周的风险分布
- **指标相关性**：物理指标间的关联分析
- **异常检测**：识别统计异常的检测结果

#### 关键函数说明

```python
def compute_detection_statistics(results: List[DetectionResult]) -> Dict[str, float]
```
- **功能**：计算检测统计信息
- **输入**：检测结果列表
- **输出**：统计指标字典
- **统计内容**：
  - 总检测次数和高风险比例
  - 平均风险评分和标准差
  - 物理指标的均值、分位数
  - 置信度分布统计

```python
def compute_correlation_matrix(results: List[DetectionResult]) -> np.ndarray
```
- **功能**：计算物理指标相关性矩阵
- **输入**：检测结果列表
- **输出**：相关性矩阵
- **应用**：验证物理指标的独立性和互补性

## 数据结构定义

### DetectionResult 数据结构
```python
@dataclass
class DetectionResult:
    timestamp: datetime         # 检测时间戳
    cell_id: str               # 电池ID
    cycle_id: int              # 循环编号
    risk_score: float          # 风险评分 [0,1]
    risk_level: str            # 风险等级
    physics_metrics: Dict      # 物理指标字典
    explanation: Dict          # 解释信息
    confidence: float          # 置信度 [0,1]
    segment_info: Optional[Dict] = None  # CV段信息(可选)
```

### BatchResult 数据结构
```python
@dataclass
class BatchResult:
    batch_id: str                    # 批次ID
    total_samples: int               # 样本总数
    high_risk_count: int             # 高风险样本数
    detection_summary: Dict          # 检测汇总
    individual_results: List[DetectionResult]  # 个体结果
    processing_time: float           # 处理时间(秒)
```

## 使用示例

### 基础结果格式化
```python
from result_output import ResultFormatter, DetectionResult

# 初始化格式化器
formatter = ResultFormatter(config={
    'risk_thresholds': {'warning': 0.7, 'critical': 0.9},
    'confidence_method': 'physics_consistency'
})

# 格式化单个结果
raw_output = {
    'risk_score': 0.85,
    'physics_metrics': {
        'S_plat': 0.78, 'S_bump': 0.62, 'S_amp': 0.012,
        'Q_tilde': 0.004, 'C_low': 0.80, 'C_kappa': 0.65
    },
    'attention_weights': attention_matrix
}

metadata = {
    'cell_id': 'Cell_001',
    'cycle_id': 1250,
    'timestamp': datetime.now()
}

result = formatter.format_single_result(raw_output, metadata)
print(f"风险等级: {result.risk_level}")
print(f"主要指标: {result.explanation['primary_indicators']}")
```

### 批量报告生成
```python
from result_output import ReportGenerator, BatchResult

# 收集批量结果
batch_result = BatchResult(
    batch_id="BATCH_20241225_001",
    total_samples=100,
    high_risk_count=8,
    individual_results=detection_results,
    processing_time=45.2
)

# 生成报告
report_gen = ReportGenerator()

# 汇总报告
summary = report_gen.generate_summary_report(batch_result)
print(f"高风险检出率: {summary['high_risk_rate']:.2%}")

# 详细报告
detailed = report_gen.generate_detailed_report(detection_results)

# 趋势分析
trend_analysis = report_gen.generate_trend_analysis(
    historical_results=recent_batches,
    time_window=7  # 最近7天
)
```

### 数据导出
```python
from result_output import DataExporter

exporter = DataExporter(output_dir='./reports')

# 导出CSV
csv_path = exporter.export_to_csv(
    results=detection_results,
    filename='lithium_detection_results.csv'
)

# 导出Excel(含图表)
excel_path = exporter.export_to_excel(
    results=detection_results,
    filename='detailed_analysis.xlsx',
    include_charts=True
)

# 导出JSON
json_path = exporter.export_to_json(
    data=batch_result,
    filename='batch_summary.json'
)

print(f"结果已导出到: {csv_path}, {excel_path}, {json_path}")
```

### 告警系统
```python
from result_output import AlertManager

# 初始化告警管理器
alert_mgr = AlertManager(alert_config={
    'email_enabled': True,
    'webhook_enabled': True,
    'recipients': ['<EMAIL>'],
    'webhook_url': 'https://api.company.com/alerts'
})

# 检查告警条件
for result in detection_results:
    alerts = alert_mgr.check_alert_conditions(result)
    
    for alert in alerts:
        # 生成告警消息
        message = alert_mgr.generate_alert_message(result, alert['type'])
        
        # 发送告警
        if alert['severity'] == 'critical':
            alert_mgr.send_email_alert(message, ['<EMAIL>'])
            alert_mgr.send_webhook_alert(message, 'https://urgent.company.com/api')
        
        # 记录告警日志
        alert_mgr.log_alert(message)
```

### 实时流导出
```python
# 实时流配置
stream_config = {
    'target': 'kafka',
    'topic': 'lithium_detection',
    'bootstrap_servers': ['localhost:9092'],
    'format': 'json'
}

# 实时推送检测结果
for result in realtime_results:
    success = exporter.export_realtime_stream(result, stream_config)
    if not success:
        print(f"推送失败: {result.cell_id}")
```

## 报告模板定制

### HTML报告模板
```html
<!DOCTYPE html>
<html>
<head>
    <title>析锂检测报告</title>
    <style>
        .risk-critical { color: #dc3545; font-weight: bold; }
        .risk-warning { color: #ffc107; font-weight: bold; }
        .risk-normal { color: #28a745; }
    </style>
</head>
<body>
    <h1>电池析锂检测报告</h1>
    <div class="summary">
        <h2>检测摘要</h2>
        <p>总样本数: {{total_samples}}</p>
        <p>高风险样本: {{high_risk_count}}</p>
        <p>检出率: {{detection_rate}}%</p>
    </div>
    <!-- 更多报告内容 -->
</body>
</html>
```

### PDF报告生成
```python
def generate_pdf_report(results, output_path):
    """生成PDF格式的检测报告"""
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Table
    
    # 创建PDF文档
    doc = SimpleDocTemplate(output_path, pagesize=letter)
    story = []
    
    # 添加标题
    title = Paragraph("电池析锂检测报告", title_style)
    story.append(title)
    
    # 添加汇总表格
    summary_data = [
        ['指标', '数值'],
        ['总检测次数', len(results)],
        ['高风险次数', sum(1 for r in results if r.risk_level == 'critical')],
        ['平均风险评分', f"{np.mean([r.risk_score for r in results]):.3f}"]
    ]
    
    summary_table = Table(summary_data)
    story.append(summary_table)
    
    # 构建PDF
    doc.build(story)
```

## 性能监控

### 输出性能指标
```python
def monitor_output_performance():
    """监控输出模块性能"""
    metrics = {
        'format_time_ms': [],      # 格式化耗时
        'export_time_ms': [],      # 导出耗时
        'report_size_mb': [],      # 报告文件大小
        'alert_latency_ms': []     # 告警延迟
    }
    
    # 收集性能数据
    for result in test_results:
        start_time = time.time()
        formatted = formatter.format_single_result(result)
        format_time = (time.time() - start_time) * 1000
        metrics['format_time_ms'].append(format_time)
    
    # 计算统计指标
    avg_format_time = np.mean(metrics['format_time_ms'])
    p95_format_time = np.percentile(metrics['format_time_ms'], 95)
    
    print(f"平均格式化时间: {avg_format_time:.2f}ms")
    print(f"P95格式化时间: {p95_format_time:.2f}ms")
```

## 质量保障

### 结果验证
```python
from result_output import ResultValidator

validator = ResultValidator()

# 验证结果格式
errors = validator.validate_result_format(result)
if errors:
    print(f"格式错误: {errors}")

# 验证物理一致性
consistency = validator.validate_physics_consistency(
    result.physics_metrics
)
if not all(consistency.values()):
    print("物理指标不一致")

# 交叉验证
validation_report = validator.cross_validate_results(results)
print(f"验证通过率: {validation_report['pass_rate']:.2%}")
```

### 输出审计
```python
def audit_output_quality(results):
    """审计输出质量"""
    audit_report = {
        'total_results': len(results),
        'format_errors': 0,
        'missing_explanations': 0,
        'confidence_issues': 0
    }
    
    for result in results:
        # 检查格式完整性
        if not all([result.risk_score, result.risk_level, result.confidence]):
            audit_report['format_errors'] += 1
            
        # 检查解释完整性
        if not result.explanation.get('primary_indicators'):
            audit_report['missing_explanations'] += 1
            
        # 检查置信度合理性
        if result.confidence < 0.5:
            audit_report['confidence_issues'] += 1
    
    return audit_report
```

## 扩展开发

### 自定义导出格式
```python
class CustomExporter(DataExporter):
    def export_to_xml(self, results, filename):
        """扩展XML导出功能"""
        # 实现XML格式导出
        pass
    
    def export_to_database(self, results, db_config):
        """扩展数据库直接导出"""
        # 实现数据库插入
        pass
```

### 自定义告警通道
```python
class SlackAlertChannel:
    def send_slack_alert(self, message, channel):
        """发送Slack告警"""
        # 实现Slack API调用
        pass

# 集成到AlertManager
alert_mgr.register_channel('slack', SlackAlertChannel())
```

该模块通过标准化的输出格式和丰富的报告功能，为析锂检测系统提供了完整的结果管理解决方案，确保检测结果能够有效传达给最终用户并指导实际决策。
