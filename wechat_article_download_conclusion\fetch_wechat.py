import sys
import json
import time
from typing import Dict, Any, List

import requests
from bs4 import BeautifulSoup


DEFAULT_HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/126.0.0.0 Safari/537.36"
    ),
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
}


def http_get(url: str, timeout: int = 30, retries: int = 2) -> str:
    last_err = None
    for attempt in range(retries + 1):
        try:
            resp = requests.get(url, headers=DEFAULT_HEADERS, timeout=timeout)
            resp.raise_for_status()
            # Heuristic encoding recovery
            if not resp.encoding:
                resp.encoding = resp.apparent_encoding or "utf-8"
            return resp.text
        except Exception as e:  # noqa: BLE001 - CLI tool, keep simple
            last_err = e
            time.sleep(1.2 * (attempt + 1))
    raise RuntimeError(f"GET failed for {url}: {last_err}")


def extract_article(html: str) -> Dict[str, Any]:
    soup = BeautifulSoup(html, "lxml")

    def text_or_empty(selector: str) -> str:
        node = soup.select_one(selector)
        return node.get_text(strip=True) if node else ""

    def meta_content(selector: str) -> str:
        node = soup.select_one(selector)
        if node and node.has_attr("content"):
            return (node["content"] or "").strip()
        return ""

    title = (
        text_or_empty("#activity-name")
        or meta_content('meta[property="og:title"]')
        or (soup.title.get_text(strip=True) if soup.title else "")
    )
    publish_time = text_or_empty("#publish_time") or meta_content(
        'meta[property="article:published_time"]'
    )
    author = text_or_empty("#js_author_name") or meta_content('meta[name="author"]')
    account = text_or_empty("#js_name") or meta_content(
        'meta[property="article:author"]'
    )

    paragraphs: List[str] = [
        p.get_text(" ", strip=True) for p in soup.select("#js_content p")
    ]
    if not paragraphs:
        paragraphs = [p.get_text(" ", strip=True) for p in soup.find_all("p")]

    # filter noise
    filtered: List[str] = []
    for s in paragraphs:
        if not s:
            continue
        if len(s) <= 5:
            continue
        if s.startswith("赞赏") or "正在加载" in s:
            continue
        filtered.append(s)

    body = "\n".join(filtered)
    image_count = len(soup.select("#js_content img")) or len(soup.find_all("img"))

    return {
        "title": title,
        "author": author,
        "account": account,
        "publish_time": publish_time,
        "images": image_count,
        "text_length": len(body),
        "excerpt": body[:800],
        "content": body,
    }


def main() -> None:
    if len(sys.argv) < 2:
        print("Usage: python fetch_wechat.py <url>")
        sys.exit(2)

    raw_url = sys.argv[1]

    # Try mirror first, then fallback to raw mp url if present inside
    candidates = [raw_url]
    if "http" in raw_url and raw_url.count("http") > 1:
        # e.g. https://mirror/https://mp.weixin...
        try:
            inner = raw_url.split("http", 1)[1]
            inner = "http" + inner
            candidates.append(inner)
        except Exception:
            pass

    html = None
    last_err = None
    for u in candidates:
        try:
            html = http_get(u)
            break
        except Exception as e:  # noqa: BLE001
            last_err = e
            continue

    if html is None:
        raise RuntimeError(f"All fetch attempts failed: {last_err}")

    data = extract_article(html)

    with open("wechat_article.json", "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    with open("wechat_article.txt", "w", encoding="utf-8") as f:
        f.write(data.get("content", ""))

    # Also print a concise line for quick inspection
    print(json.dumps({k: data[k] for k in [
        "title", "author", "account", "publish_time", "text_length"
    ]}, ensure_ascii=False))


if __name__ == "__main__":
    main()

