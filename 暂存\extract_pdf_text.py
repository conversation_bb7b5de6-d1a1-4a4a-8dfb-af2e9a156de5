import sys
import os
import argparse


def try_imports():
    mods = []
    try:
        import pypdf  # type: ignore
        mods.append(('pypdf', pypdf))
    except Exception:
        pass
    try:
        import PyPDF2  # type: ignore
        mods.append(('PyPDF2', PyPDF2))
    except Exception:
        pass
    try:
        from pdfminer import high_level as pdfminer_high_level  # type: ignore
        mods.append(('pdfminer.high_level', pdfminer_high_level))
    except Exception:
        pass
    return mods


def extract_with_pypdf(pdf_path):
    from pypdf import PdfReader  # type: ignore
    reader = PdfReader(pdf_path)
    texts = []
    for i, page in enumerate(reader.pages):
        try:
            texts.append(page.extract_text() or "")
        except Exception as e:
            texts.append(f"\n[WARN] Failed to extract page {i+1}: {e}\n")
    return "\n\n".join(texts)


def extract_with_pypdf2(pdf_path):
    from PyPDF2 import PdfReader  # type: ignore
    reader = PdfReader(pdf_path)
    texts = []
    for i, page in enumerate(reader.pages):
        try:
            texts.append(page.extract_text() or "")
        except Exception as e:
            texts.append(f"\n[WARN] Failed to extract page {i+1}: {e}\n")
    return "\n\n".join(texts)


def extract_with_pdfminer(pdf_path):
    from pdfminer.high_level import extract_text  # type: ignore
    return extract_text(pdf_path)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('pdf_path', help='Path to PDF file')
    parser.add_argument('-o', '--output', default='extracted_text.txt', help='Output text file')
    args = parser.parse_args()

    pdf_path = args.pdf_path
    if not os.path.exists(pdf_path):
        print(f"[ERROR] PDF not found: {pdf_path}")
        sys.exit(2)

    mods = try_imports()
    if not mods:
        print('[ERROR] No PDF extraction library available (pypdf, PyPDF2, or pdfminer.six).')
        print('        Please allow installing a minimal dependency (recommended: pypdf).')
        sys.exit(3)

    # Prefer pypdf, then PyPDF2, then pdfminer
    extractors = []
    for name, mod in mods:
        if name == 'pypdf':
            extractors.append(('pypdf', extract_with_pypdf))
    for name, mod in mods:
        if name == 'PyPDF2':
            extractors.append(('PyPDF2', extract_with_pypdf2))
    for name, mod in mods:
        if name == 'pdfminer.high_level':
            extractors.append(('pdfminer.high_level', extract_with_pdfminer))

    last_err = None
    for name, fn in extractors:
        try:
            print(f"[INFO] Using extractor: {name}")
            text = fn(pdf_path)
            with open(args.output, 'w', encoding='utf-8', errors='ignore') as f:
                f.write(text)
            print(f"[SUCCESS] Text extracted to: {args.output}")
            return
        except Exception as e:
            last_err = e
            print(f"[WARN] Extractor {name} failed: {e}")

    print(f"[ERROR] All available extractors failed. Last error: {last_err}")
    sys.exit(4)


if __name__ == '__main__':
    main()

