# 电池数据分析工具 / Battery Data Analysis Tool

这是一个专门用于电池循环测试数据分析的Python工具，支持从JSON文件中提取数据、计算dI/dt、检测膝点，并生成综合分析图表。

This is a Python tool specifically designed for battery cycle test data analysis, supporting data extraction from JSON files, dI/dt calculation, knee point detection, and comprehensive analysis chart generation.

## 主要功能 / Main Features

1. **数据提取** / **Data Extraction**
   - 从JSON文件中提取电池循环数据
   - 支持时间单位转换（分钟→秒）
   - 提取容量和库伦效率(CE)数据

2. **dI/dt计算** / **dI/dt Calculation**
   - 向量化高性能计算
   - Savitzky-Golay滤波平滑处理
   - 异常值过滤和数据验证

3. **膝点检测** / **Knee Point Detection**
   - 基于曲率分析的自动膝点检测
   - 识别电池容量衰减的关键转折点

4. **数据可视化** / **Data Visualization**
   - 四象限图表布局：电流图、dI/dt图、容量+CE图、容量变化率图
   - **渐变色显示**：电流和dI/dt图使用viridis渐变色谱
   - **双Y轴显示**：容量图左轴显示容量，右轴显示CE值
   - **容量变化率分析**：第四象限显示容量变化率而非膝点分析
   - 支持指定循环标记（黑色高亮显示）
   - **可调Y轴范围**：支持自定义电流图和dI/dt图的Y轴范围
   - 自动保存高分辨率图片

## 文件结构 / File Structure

```
├── battery_analysis_direct_plot.py  # 主分析工具
├── example_usage.py                 # 使用示例和测试
├── README.md                        # 说明文档
└── your_battery_data.json          # 您的数据文件（需要准备）
```

## 快速开始 / Quick Start

### 1. 准备数据文件 / Prepare Data File

您的JSON文件应该包含以下结构：
Your JSON file should contain the following structure:

```json
{
  "Cycle_1": {
    "relative_time_min": [0, 0.1, 0.2, ...],
    "current_A": [-1.0, -0.95, -0.9, ...]
  },
  "Cycle_2": {
    "relative_time_min": [0, 0.1, 0.2, ...],
    "current_A": [-1.0, -0.95, -0.9, ...]
  },
  ...
  "Discharge Capacity": [1.0, 0.98, 0.96, ...],
  "CE": [0.98, 0.99, 0.97, ...]
}
```

### 2. 修改配置参数 / Modify Configuration Parameters

在 `battery_analysis_direct_plot.py` 的 `main()` 函数中修改以下参数：
Modify the following parameters in the `main()` function of `battery_analysis_direct_plot.py`:

```python
# 配置参数 - 用户可以修改这些参数
JSON_FILE_PATH = "your_battery_data.json"  # 修改为您的JSON文件路径
SAMPLE_NAME = "Battery_Sample_1"           # 修改为您的样品名称
KCLS = [1, 10, 50, 100]                   # 要标记的循环数列表
DOWNSAMPLE_FACTOR = 1                      # 下采样因子，1表示不下采样
SAVE_FIGURE = True                         # 是否保存图片
OUTPUT_PATH = f"{SAMPLE_NAME}_analysis.png" # 输出图片路径
```

### 3. 运行分析 / Run Analysis

#### 方法1：直接运行主程序 / Method 1: Run Main Program Directly
```bash
python battery_analysis_direct_plot.py
```

#### 方法2：使用示例程序 / Method 2: Use Example Program
```bash
python example_usage.py
```

示例程序会询问是否创建测试数据，适合初次使用。
The example program will ask if you want to create test data, suitable for first-time use.

## 输出结果 / Output Results

程序会生成一个包含四个子图的综合分析图表：
The program will generate a comprehensive analysis chart with four subplots:

1. **电流vs时间图** / **Current vs Time Plot** (左上)
   - 使用viridis渐变色显示所有循环的电流曲线
   - 标记的循环用黑色粗线显示
   - 支持自定义Y轴范围

2. **dI/dt vs时间图** / **dI/dt vs Time Plot** (右上)
   - 使用viridis渐变色显示电流变化率曲线
   - 经过平滑处理，减少噪声
   - 支持自定义Y轴范围

3. **容量vs循环图** / **Capacity vs Cycle Plot** (左下)
   - 左Y轴：显示容量衰减趋势（蓝色）
   - 右Y轴：显示库伦效率CE值（红色）
   - 标记自动检测的膝点和指定循环

4. **容量变化率图** / **Capacity Change Rate Plot** (右下)
   - 显示容量变化率 (Ah/cycle)
   - 包含零参考线
   - 标记膝点和指定循环

## 参数说明 / Parameter Description

### 主要参数 / Main Parameters

- `JSON_FILE_PATH`: JSON数据文件路径
- `SAMPLE_NAME`: 样品名称，用于图表标题和文件名
- `KCLS`: 要标记的循环数列表，例如 `[1, 10, 50, 100]`
- `DOWNSAMPLE_FACTOR`: 下采样因子，用于减少数据点数量
- `SAVE_FIGURE`: 是否保存图片到文件
- `OUTPUT_PATH`: 输出图片的文件路径

### 新增Y轴范围参数 / New Y-axis Range Parameters

- `CURRENT_YLIM`: 电流图Y轴范围，例如 `(-2.0, 0.5)` 或 `None`（自动）
- `DIDT_YLIM`: dI/dt图Y轴范围，例如 `(-0.1, 0.01)` 或 `None`（自动）

### 高级参数 / Advanced Parameters

- `figsize`: 图片尺寸，默认 `(15, 12)`
- `dpi`: 图片分辨率，默认 `100`
- `internal`: dI/dt计算的内部参数，默认 `50`

## 数据标记功能 / Data Marking Feature

通过设置 `KCLS` 参数，您可以在所有图表中标记特定的循环：
By setting the `KCLS` parameter, you can mark specific cycles in all charts:

- 标记的循环在电流图和dI/dt图中用**黑色粗线**显示
- 在容量图中用**黑色圆点**标记
- 在膝点分析图中用**黑色方块**标记
- 自动添加图例说明

## 故障排除 / Troubleshooting

### 常见问题 / Common Issues

1. **文件未找到错误**
   - 检查 `JSON_FILE_PATH` 是否正确
   - 确保文件存在且可读

2. **数据格式错误**
   - 检查JSON文件格式是否正确
   - 确保包含必要的字段：`Cycle_X`, `relative_time_min`, `current_A`

3. **内存不足**
   - 增加 `DOWNSAMPLE_FACTOR` 的值
   - 减少处理的循环数量

4. **图表显示问题**
   - 确保安装了matplotlib
   - 检查显示环境是否支持图形界面

### 依赖库 / Dependencies

```bash
pip install numpy pandas matplotlib scipy
```

## 示例输出 / Example Output

运行成功后，您会看到类似以下的日志输出：
After successful execution, you will see log output similar to the following:

```
[2024-01-01 12:00:00] Starting battery data analysis...
[2024-01-01 12:00:01] Loading JSON file: your_battery_data.json
[2024-01-01 12:00:02] Extracting cycle data from JSON...
[2024-01-01 12:00:03] Calculating dI/dt for all cycles...
[2024-01-01 12:00:05] Detected knee points at cycles: [25, 67, 89]
[2024-01-01 12:00:06] Creating battery analysis plots...
[2024-01-01 12:00:07] Figure saved to: Battery_Sample_1_analysis.png
[2024-01-01 12:00:07] Analysis completed successfully!

Summary:
  - Total cycles processed: 100
  - Cycles with dI/dt data: 98
  - Cycles with capacity data: 100
  - Auto-detected knee points: [25, 67, 89]
  - Marked cycles: [1, 10, 50, 100]
```

## 技术支持 / Technical Support

如果您在使用过程中遇到问题，请检查：
If you encounter issues during use, please check:

1. 数据文件格式是否正确
2. 所有依赖库是否已安装
3. Python版本是否兼容（推荐Python 3.7+）

## 更新日志 / Changelog

- v1.0: 初始版本，支持基本的数据分析和可视化功能
- v1.1: 添加了数据标记功能和膝点检测
- v1.2: 优化了性能和用户体验
