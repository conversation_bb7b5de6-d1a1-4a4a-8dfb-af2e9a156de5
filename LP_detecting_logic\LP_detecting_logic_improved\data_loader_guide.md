# 数据读取模块使用指南

## 概述

`data_loader.py` 模块负责从各种数据源读取和预处理电池测试数据，提供统一的数据接口和质量保障功能。

## 核心类介绍

### 1. BatteryDataLoader 类

电池数据的主要加载器，支持多种数据格式和来源。

#### 主要功能
- **多格式支持**：CSV、MATLAB .mat、HDF5等格式
- **实时数据流**：支持BMS实时数据接入
- **数据预处理**：重采样、滤波、循环切分
- **格式验证**：确保数据完整性和正确性

#### 关键函数说明

```python
def load_csv_data(file_path: str) -> Dict[str, np.ndarray]
```
- **功能**：从CSV文件加载电池数据
- **输入**：CSV文件路径
- **输出**：包含t, I, V, Q的字典
- **适用场景**：实验室数据、标准测试数据

```python
def load_realtime_stream(bms_config: Dict) -> Dict[str, np.ndarray]
```
- **功能**：从BMS系统获取实时数据流
- **输入**：BMS连接配置
- **输出**：实时数据流
- **适用场景**：在线监控、实时检测

```python
def split_cycles(data: Dict[str, np.ndarray]) -> List[Dict[str, np.ndarray]]
```
- **功能**：将连续数据按充放电循环切分
- **输入**：连续的电池数据
- **输出**：按循环切分的数据列表
- **适用场景**：长期测试数据处理

### 2. DataValidator 类

数据质量验证器，确保输入数据的可靠性。

#### 主要功能
- **完整性检查**：检测缺失值和数据完整性
- **异常值检测**：识别数据中的异常点
- **物理约束验证**：验证是否符合电池物理特性
- **采样一致性**：检查时间序列采样规律

#### 关键函数说明

```python
def check_data_completeness(data: Dict[str, np.ndarray]) -> Dict[str, float]
```
- **功能**：检查各字段的数据完整性
- **输入**：待检查的数据字典
- **输出**：各字段完整性百分比
- **用途**：评估数据质量，决定是否可用于分析

```python
def validate_physical_constraints(data: Dict[str, np.ndarray]) -> List[str]
```
- **功能**：验证数据是否违反物理约束
- **输入**：电池数据
- **输出**：违反约束的警告信息列表
- **用途**：确保数据物理合理性

### 3. DataCache 类

数据缓存管理器，提高数据访问效率。

#### 主要功能
- **内存缓存**：将频繁访问的数据缓存到内存
- **智能淘汰**：LRU策略管理缓存空间
- **统计监控**：提供缓存命中率等统计信息

## 使用示例

### 基础数据加载
```python
from data_loader import BatteryDataLoader

# 初始化加载器
loader = BatteryDataLoader(config={'sampling_rate': 1.0})

# 加载CSV数据
data = loader.load_csv_data('battery_test.csv')

# 验证数据格式
is_valid = loader.validate_data_format(data)

# 按循环切分
cycles = loader.split_cycles(data)
```

### 实时数据流处理
```python
# 配置BMS连接
bms_config = {
    'host': '*************',
    'port': 8080,
    'protocol': 'modbus'
}

# 获取实时数据
stream_data = loader.load_realtime_stream(bms_config)
```

### 数据质量验证
```python
from data_loader import DataValidator

validator = DataValidator()

# 检查数据完整性
completeness = validator.check_data_completeness(data)
print(f"数据完整性: {completeness}")

# 检测异常值
outliers = validator.detect_outliers(data)
print(f"检测到异常值: {len(outliers['I'])} 个")

# 验证物理约束
warnings = validator.validate_physical_constraints(data)
if warnings:
    print(f"物理约束警告: {warnings}")
```

## 配置参数

### 加载器配置
```python
config = {
    'sampling_rate': 1.0,           # 目标采样率 (Hz)
    'filter_type': 'butterworth',   # 滤波器类型
    'filter_order': 3,              # 滤波器阶数
    'cycle_detection_method': 'current_sign',  # 循环检测方法
    'data_format': 'standard'       # 数据格式标准
}
```

### 验证器参数
```python
validation_config = {
    'completeness_threshold': 0.95,  # 完整性阈值
    'outlier_method': 'iqr',        # 异常检测方法
    'outlier_factor': 1.5,          # 异常检测因子
    'voltage_range': [2.5, 4.2],    # 电压合理范围
    'current_range': [-50, 50]      # 电流合理范围 (A)
}
```

## 性能优化

### 大文件处理
- 使用分块读取避免内存溢出
- 启用数据缓存提高重复访问效率
- 异步加载提升响应速度

### 实时数据处理
- 配置合适的缓冲区大小
- 使用多线程处理并发数据流
- 实施数据压缩减少传输开销

## 错误处理

### 常见错误类型
1. **文件格式错误**：不支持的文件格式或损坏文件
2. **数据格式错误**：缺少必需字段或字段类型不匹配
3. **网络连接错误**：BMS连接失败或超时
4. **内存不足**：处理大文件时内存溢出

### 错误处理策略
```python
try:
    data = loader.load_csv_data(file_path)
except FileNotFoundError:
    print("文件未找到，请检查路径")
except ValueError as e:
    print(f"数据格式错误: {e}")
except MemoryError:
    print("内存不足，建议分块处理")
```

## 最佳实践

1. **数据预处理**：加载后立即进行格式验证和质量检查
2. **缓存策略**：对频繁访问的数据启用缓存
3. **错误处理**：实施完善的异常处理机制
4. **性能监控**：定期检查加载性能和缓存命中率
5. **数据备份**：重要数据进行多重备份保护

## 依赖要求

```
numpy >= 1.19.0
pandas >= 1.3.0
h5py >= 3.1.0          # HDF5支持
scipy >= 1.7.0         # 信号处理
matplotlib >= 3.3.0    # 可视化支持
```

## 扩展说明

该模块设计为可扩展架构，支持：
- 新数据格式的插件式添加
- 自定义数据验证规则
- 多种实时数据源接入
- 分布式数据处理支持
