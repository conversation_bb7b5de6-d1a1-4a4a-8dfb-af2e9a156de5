# 析锂检测数据读取模块使用指南

## 概述

`data_loader.py` 模块专为基于物理先验的Transformer析锂检测技术方案设计，负责从各种数据源读取和预处理电池测试数据，支持CV段识别、物理软指标计算和实时流式处理。

## 核心特性

- **CV段自动识别**: 恒压阶段的智能检测和切分
- **7维物理软指标**: 支持析锂检测的完整物理特征计算
- **实时流式处理**: 滑窗机制和物理触发条件
- **渐进式训练支持**: 弱监督学习的数据准备
- **物理约束验证**: 确保数据符合析锂检测的物理特性

## 核心类介绍

### 1. LithiumDetectionDataLoader 类

专为析锂检测设计的电池数据加载器，集成CV段识别和物理软指标计算。

#### 主要功能
- **多格式支持**：CSV、MATLAB .mat、HDF5等格式
- **CV段智能识别**：基于电压方差和电流斜率的双重门控策略
- **物理软指标计算**：7维软指标向量的自动计算
- **实时流式处理**：支持BMS实时数据接入和滑窗处理
- **渐进式训练数据**：自动标签生成和弱监督学习支持
- **物理约束验证**：确保数据符合析锂检测的物理特性

#### 关键函数说明

```python
def load_with_cv_detection(file_path: str) -> Dict[str, Union[np.ndarray, List]]
```
- **功能**：加载数据并自动识别CV段
- **输入**：数据文件路径
- **输出**：包含原始数据和CV段边界的字典
- **适用场景**：析锂检测的训练数据准备

```python
def compute_physics_soft_metrics(cv_data: Dict) -> Dict[str, float]
```
- **功能**：计算7维物理软指标向量
- **输入**：CV段数据
- **输出**：{'S_plat': float, 'S_bump': float, 'S_mono': float, 'S_amp': float, 'Q_tilde': float, 'C_low': float, 'C_kappa': float}
- **适用场景**：物理特征提取和弱监督标签生成

```python
def create_sliding_windows(data: Dict, window_size: int, step_size: int) -> Iterator
```
- **功能**：创建滑窗数据流用于实时检测
- **输入**：时序数据、窗口大小、步长
- **输出**：滑窗数据迭代器
- **适用场景**：在线析锂检测、实时监控

```python
def generate_weak_supervision_labels(physics_metrics: Dict) -> Dict[str, float]
```
- **功能**：基于物理软指标生成弱监督标签
- **输入**：物理软指标字典
- **输出**：包含软标签和硬标签的字典
- **适用场景**：无标注数据的训练标签自动生成

### 2. LithiumDataValidator 类

专为析锂检测优化的数据质量验证器，集成物理约束和CV段质量检查。

#### 主要功能
- **完整性检查**：检测缺失值和数据完整性
- **CV段质量验证**：检查恒压阶段数据的有效性
- **物理约束验证**：验证析锂检测的物理特性
- **采样一致性**：检查时间序列采样规律
- **软指标合理性**：验证7维软指标的计算正确性
- **实时数据流验证**：在线数据质量监控

#### 关键函数说明

```python
def validate_cv_segment_quality(cv_segments: List[Dict]) -> Dict[str, float]
```
- **功能**：验证CV段数据质量
- **输入**：CV段数据列表
- **输出**：质量评分字典（电压稳定性、电流单调性、段长度等）
- **用途**：确保CV段适合析锂检测分析

```python
def check_lithium_physics_constraints(data: Dict[str, np.ndarray]) -> Dict[str, bool]
```
- **功能**：验证析锂检测的物理约束
- **输入**：电池数据
- **输出**：物理约束检查结果
- **约束条件**：
  - 恒压阶段电压稳定性 (var(V) < 1e-4)
  - 电流单调衰减性 (dI/dt < 0)
  - 电流正值约束 (I > 0)
  - 容量单调性 (dQ/dt ≥ 0)

```python
def validate_soft_metrics_range(metrics: Dict[str, float]) -> Dict[str, bool]
```
- **功能**：验证7维软指标的合理性
- **输入**：物理软指标字典
- **输出**：各指标的合理性检查结果
- **检查范围**：
  - S_plat, S_bump, S_mono, S_amp ∈ [0, 1]
  - C_low, C_kappa ∈ [0, 1]
  - Q_tilde ≥ 0

### 3. PhysicsMetricsCache 类

专为析锂检测优化的数据缓存管理器，支持物理软指标和CV段的高效缓存。

#### 主要功能
- **CV段缓存**：缓存已识别的CV段边界和质量评分
- **软指标缓存**：缓存计算好的7维物理软指标
- **实时数据缓存**：滑窗数据的循环缓冲区
- **智能淘汰**：基于访问频率和计算成本的缓存策略
- **物理一致性**：缓存物理约束验证结果

## 使用示例

### 析锂检测数据加载流程
```python
from data_loader import LithiumDetectionDataLoader, LithiumDataValidator

# 初始化析锂检测专用加载器
loader = LithiumDetectionDataLoader(config={
    'sampling_rate': 1.0,
    'cv_detection_params': {
        'v_var_threshold': 1e-4,
        'i_slope_threshold': -1e-5,
        'min_duration': 30
    },
    'physics_params': {
        'tau_slope': 0.002,
        'kappa_pos': 0.0005,
        'kappa_neg': -0.0005
    }
})

# 加载数据并自动识别CV段
result = loader.load_with_cv_detection('battery_charging_data.csv')
raw_data = result['raw_data']  # 原始数据
cv_segments = result['cv_segments']  # CV段边界
cv_quality = result['cv_quality']  # CV段质量评分

# 计算7维物理软指标
for i, segment in enumerate(cv_segments):
    physics_metrics = loader.compute_physics_soft_metrics(segment)
    print(f"CV段 {i+1} 物理指标:")
    print(f"  平台度: {physics_metrics['S_plat']:.3f}")
    print(f"  凸起度: {physics_metrics['S_bump']:.3f}")
    print(f"  回嵌幅度: {physics_metrics['S_amp']:.3f}")
```

### 弱监督标签生成
```python
# 自动生成训练标签
weak_labels = loader.generate_weak_supervision_labels(physics_metrics)

print(f"软标签: {weak_labels['soft_label']:.3f}")
print(f"硬标签: {weak_labels['hard_label']}")
print(f"置信度: {weak_labels['confidence']:.3f}")
```

### 实时流式检测
```python
# 创建滑窗数据流
window_stream = loader.create_sliding_windows(
    data=raw_data,
    window_size=30,  # 30秒窗口
    step_size=5      # 5秒步长
)

# 实时处理
for window_data in window_stream:
    # 检查是否为CV段
    if loader.is_cv_phase(window_data):
        # 计算物理指标
        metrics = loader.compute_physics_soft_metrics(window_data)
        # 生成预警
        if metrics['S_amp'] > 0.7 or metrics['S_plat'] > 0.7:
            print("⚠️ 检测到潜在析锂风险!")
```

### 数据质量验证
```python
validator = LithiumDataValidator()

# CV段质量验证
cv_quality = validator.validate_cv_segment_quality(cv_segments)
print(f"CV段质量评分: {cv_quality}")

# 物理约束检查
physics_check = validator.check_lithium_physics_constraints(raw_data)
print(f"物理约束验证: {physics_check}")

# 软指标合理性检查
metrics_check = validator.validate_soft_metrics_range(physics_metrics)
print(f"软指标合理性: {metrics_check}")
```

## 配置参数

### 析锂检测加载器配置
```python
lithium_config = {
    # 基础数据参数
    'sampling_rate': 1.0,           # 目标采样率 (Hz)
    'filter_type': 'savgol',        # 滤波器类型 (Savitzky-Golay)
    'filter_window': 5,             # 滤波窗口长度
    'filter_order': 2,              # 滤波器阶数
    
    # CV段识别参数
    'cv_detection_params': {
        'v_var_threshold': 1e-4,    # 电压方差阈值
        'i_slope_threshold': -1e-5, # 电流斜率阈值
        'min_duration': 30,         # 最小段时长 (秒)
        'adaptive_quantile': 0.1    # 自适应分位数
    },
    
    # 物理软指标参数
    'physics_params': {
        'tau_slope': 0.002,         # 平台斜率阈值
        'kappa_pos': 0.0005,        # 正曲率检测阈值
        'kappa_neg': -0.0005,       # 负曲率检测阈值
        'alpha_baseline': 0.98,     # 基线衰减因子
        'sigma_p': 0.001,           # 平台度计算参数
        'sigma_b': 0.001,           # 凸起度计算参数
        'i_threshold': 0.1          # 低电流阈值 (A)
    },
    
    # 实时处理参数
    'realtime_params': {
        'window_size': 30,          # 滑窗大小 (秒)
        'step_size': 5,             # 滑窗步长 (秒)
        'physics_activation_threshold': 0.3,  # 物理激活阈值
        'buffer_size': 1000         # 数据缓冲区大小
    }
}
```

### 验证器参数
```python
validation_config = {
    # 基础验证参数
    'completeness_threshold': 0.95,  # 完整性阈值
    'outlier_method': 'iqr',        # 异常检测方法
    'outlier_factor': 1.5,          # 异常检测因子
    
    # 析锂检测物理约束
    'lithium_constraints': {
        'voltage_range': [3.0, 4.2],    # CV段电压范围 (V)
        'current_range': [0.001, 10],   # CV段电流范围 (A)
        'voltage_stability': 1e-4,      # 电压稳定性要求
        'current_monotonicity': -1e-6,  # 电流单调性要求
        'min_cv_points': 20             # CV段最小数据点数
    },
    
    # 软指标验证范围
    'metrics_ranges': {
        'S_plat': [0, 1],           # 平台度范围
        'S_bump': [0, 1],           # 凸起度范围
        'S_mono': [0, 1],           # 非单调性范围
        'S_amp': [0, 1],            # 回嵌幅度范围
        'Q_tilde': [0, float('inf')], # 锂量代理范围
        'C_low': [0, 1],            # 低电流一致性范围
        'C_kappa': [0, 1]           # 曲率一致性范围
    }
}
```

## 性能优化

### 析锂检测专用优化
- **CV段缓存**: 缓存已计算的CV段边界，避免重复识别
- **软指标预计算**: 对常用数据预计算7维物理软指标
- **物理约束快速检查**: 优先进行轻量级物理约束验证
- **自适应精度**: 根据数据质量动态调整计算精度

### 实时流式优化
- **循环缓冲区**: 使用固定大小缓冲区避免内存持续增长
- **物理触发**: 仅在检测到潜在CV段时激活重计算
- **多级缓存**: CV段检测、软指标计算、模型推理的分级缓存
- **并行处理**: 数据预处理与物理计算的并行执行

### 大文件处理优化
- **分段加载**: 按循环或时间段分块处理大文件
- **智能采样**: 根据数据密度自适应采样策略
- **内存映射**: 对超大文件使用内存映射减少内存占用

## 错误处理

### 析锂检测特有错误类型
1. **CV段识别失败**：数据中无有效恒压阶段
2. **物理约束违反**：数据不符合析锂检测的物理特性
3. **软指标计算异常**：数值计算中的异常值或发散
4. **实时数据流中断**：BMS连接失败或数据质量异常
5. **缓存一致性错误**：缓存数据与实际数据不匹配

### 错误处理策略
```python
from data_loader import LithiumDetectionDataLoader, LithiumDetectionError

try:
    loader = LithiumDetectionDataLoader(config)
    result = loader.load_with_cv_detection(file_path)
    
except FileNotFoundError:
    print("文件未找到，请检查路径")
except LithiumDetectionError.CVSegmentNotFound:
    print("未检测到有效CV段，数据可能不适合析锂检测")
except LithiumDetectionError.PhysicsConstraintViolation as e:
    print(f"物理约束违反: {e.constraint_name}")
except LithiumDetectionError.SoftMetricsCalculationError as e:
    print(f"软指标计算异常: {e.metric_name}, 值: {e.invalid_value}")
except MemoryError:
    print("内存不足，建议分块处理")
```

## 最佳实践

### 析锂检测数据处理最佳实践
1. **CV段质量预检**：加载数据后立即验证CV段质量和数量
2. **物理约束优先**：优先进行物理约束检查，过滤无效数据
3. **软指标缓存**：对计算密集的7维软指标启用智能缓存
4. **渐进式处理**：对大文件采用分段处理策略
5. **实时监控**：持续监控数据流质量和处理性能
6. **异常处理**：建立完善的异常检测和恢复机制

### 性能优化建议
- **数据预处理**: 使用Savitzky-Golay滤波替代传统滤波
- **CV段检测**: 采用自适应阈值提高检测准确性
- **软指标计算**: 使用向量化计算提升性能
- **内存管理**: 实施循环缓冲区避免内存泄漏

## 依赖要求

```
# 基础计算库
numpy >= 1.19.0
pandas >= 1.3.0
scipy >= 1.7.0         # 信号处理和数值计算

# 数据存储支持
h5py >= 3.1.0          # HDF5文件支持
tables >= 3.6.0        # PyTables支持

# 析锂检测专用
scikit-learn >= 1.0.0  # 聚类算法（GMM等）
numba >= 0.56.0        # 加速数值计算
filterpy >= 1.4.5      # 高级滤波算法

# 可视化支持
matplotlib >= 3.3.0    # 基础绘图
plotly >= 5.0.0        # 交互式可视化

# 实时处理
asyncio >= 3.4.0       # 异步处理
threading >= 3.7.0     # 多线程支持
```

## 扩展说明

该模块专为析锂检测优化，支持：
- **多电池类型**: 支持不同化学体系的电池数据
- **自定义物理模型**: 可扩展的物理软指标计算框架
- **实时数据源**: 多种BMS协议的接入适配
- **分布式处理**: 支持多节点并行数据处理
- **云端集成**: 支持云端数据存储和计算资源
