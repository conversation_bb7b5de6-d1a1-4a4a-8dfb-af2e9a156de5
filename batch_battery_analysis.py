"""
批量电池数据分析脚本
Batch Battery Data Analysis Script

自动处理指定路径下的所有JSON文件，生成对应的分析图表
Automatically process all JSON files in a specified path and generate corresponding analysis charts
"""

import os
import glob
from pathlib import Path
from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)

def extract_sample_name_from_filename(json_file_path):
    """从文件路径中提取样品名称"""
    # 获取文件名（不含扩展名）
    filename = Path(json_file_path).stem
    
    # 移除常见的后缀
    suffixes_to_remove = ['_CV_Qcha_CE', '_data', '_battery', '_cycle']
    sample_name = filename
    
    for suffix in suffixes_to_remove:
        if sample_name.endswith(suffix):
            sample_name = sample_name[:-len(suffix)]
    
    return sample_name

def process_single_file(json_file_path, output_dir, config):
    """处理单个JSON文件"""
    try:
        # 提取样品名称
        sample_name = extract_sample_name_from_filename(json_file_path)
        
        # 设置输出路径
        output_filename = f"{sample_name}_analysis.png"
        output_path = os.path.join(output_dir, output_filename)
        
        log(f"="*80)
        log(f"处理文件: {json_file_path}")
        log(f"样品名称: {sample_name}")
        log(f"输出路径: {output_path}")
        log(f"="*80)
        
        # Step 1: 提取数据
        log("Step 1: 提取数据...")
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(json_file_path)
        
        if all_cycles_data is None:
            log(f"错误: 无法从文件 {json_file_path} 提取数据")
            return False
        
        # Step 2: 计算dI/dt
        log("Step 2: 计算dI/dt...")
        didt_cycles_data = process_dIdt_data(
            all_cycles_data, 
            downsample_params=config['downsample_params']
        )
        
        # Step 3: 检测膝点
        log("Step 3: 检测膝点...")
        knee_points = detect_knee_points(cycle_capacities)
        if knee_points:
            log(f"检测到膝点: {knee_points}")
        else:
            log("未检测到膝点")
        
        # Step 4: 创建图表
        log("Step 4: 创建图表...")
        fig = create_battery_analysis_plots(
            all_cycles_data=all_cycles_data,
            didt_cycles_data=didt_cycles_data,
            cycle_capacities=cycle_capacities,
            cycle_ce_values=cycle_ce_values,
            knee_points=knee_points,
            Kcls=config['kcls'],
            sample_name=sample_name,
            figsize=config['figsize'],
            dpi=config['dpi'],
            save_path=output_path,
            current_ylim=config['current_ylim'],
            didt_ylim=config['didt_ylim'],
            capacity_ylim=config['capacity_ylim'],
            ce_ylim=config['ce_ylim'],
            change_rate_ylim=config['change_rate_ylim'],
            colormap=config['colormap'],
        )
        
        log(f"✓ 文件 {sample_name} 处理完成!")
        log(f"  - 总循环数: {len(all_cycles_data)}")
        log(f"  - dI/dt数据循环数: {len(didt_cycles_data)}")
        log(f"  - 容量数据循环数: {len(cycle_capacities)}")
        log(f"  - CE数据循环数: {len(cycle_ce_values) if cycle_ce_values else 0}")
        log(f"  - 图片已保存: {output_path}")
        
        return True
        
    except Exception as e:
        log(f"处理文件 {json_file_path} 时出错: {str(e)}")
        import traceback
        log(traceback.format_exc())
        return False

def batch_process_battery_data(input_path, config=None):
    """批量处理电池数据"""
    
    # 默认配置
    default_config = {
        'kcls': [2, 20, 50, 150],  # 要标记的循环数
        'downsample_params': {
            'i_threshold': 1,
            'factor_high': 5,
            'factor_low': 3
        },
        'colormap': 'Blues',
        'figsize': (15, 12),
        'dpi': 330,
        'current_ylim': (0, 2),
        'didt_ylim': (-0.006, 0.001),
        'capacity_ylim': None,
        'ce_ylim': (0.98, 1.01),
        'change_rate_ylim': None,
    }
    
    # 合并用户配置
    if config:
        default_config.update(config)
    config = default_config
    
    # 验证输入路径
    if not os.path.exists(input_path):
        log(f"错误: 路径 {input_path} 不存在")
        return
    
    # 查找所有JSON文件
    if os.path.isfile(input_path):
        # 如果输入的是单个文件
        json_files = [input_path]
        output_dir = os.path.dirname(input_path)
    else:
        # 如果输入的是目录
        json_pattern = os.path.join(input_path, "*.json")
        json_files = glob.glob(json_pattern)
        output_dir = input_path
    
    if not json_files:
        log(f"在路径 {input_path} 中未找到JSON文件")
        return
    
    log(f"找到 {len(json_files)} 个JSON文件")
    log(f"输出目录: {output_dir}")
    
    # 处理统计
    total_files = len(json_files)
    successful_files = 0
    failed_files = []
    
    # 批量处理
    for i, json_file in enumerate(json_files, 1):
        log(f"\n处理进度: {i}/{total_files}")
        
        success = process_single_file(json_file, output_dir, config)
        
        if success:
            successful_files += 1
        else:
            failed_files.append(json_file)
    
    # 输出处理结果总结
    log(f"\n" + "="*80)
    log(f"批量处理完成!")
    log(f"="*80)
    log(f"处理总结:")
    log(f"  - 总文件数: {total_files}")
    log(f"  - 成功处理: {successful_files}")
    log(f"  - 处理失败: {len(failed_files)}")
    log(f"  - 成功率: {successful_files/total_files*100:.1f}%")
    
    if failed_files:
        log(f"\n失败的文件:")
        for failed_file in failed_files:
            log(f"  - {failed_file}")
    
    log(f"\n所有图片已保存到: {output_dir}")

def main():
    """主函数 - 交互式批量处理"""
    print("电池数据批量分析工具")
    print("Battery Data Batch Analysis Tool")
    print("="*60)
    
    # 获取用户输入
    while True:
        input_path = input("\n请输入JSON文件路径或包含JSON文件的目录路径:\n").strip().strip('"')
        
        if os.path.exists(input_path):
            break
        else:
            print(f"错误: 路径 '{input_path}' 不存在，请重新输入")
    
    # 询问是否使用自定义配置
    use_custom = input("\n是否使用自定义配置? (y/n, 默认n): ").strip().lower()
    
    custom_config = {}
    if use_custom in ['y', 'yes', '是']:
        print("\n自定义配置 (直接回车使用默认值):")
        
        # 标记循环配置
        kcls_input = input("要标记的循环数 (例如: 2,20,50,150): ").strip()
        if kcls_input:
            try:
                custom_config['kcls'] = [int(x.strip()) for x in kcls_input.split(',')]
            except:
                print("循环数格式错误，使用默认值")
        
        # 颜色映射配置
        colormap_input = input("颜色映射 (viridis/plasma/coolwarm/jet/Blues等): ").strip()
        if colormap_input:
            custom_config['colormap'] = colormap_input
        
        # DPI配置
        dpi_input = input("图片DPI (例如: 330): ").strip()
        if dpi_input:
            try:
                custom_config['dpi'] = int(dpi_input)
            except:
                print("DPI格式错误，使用默认值")
        
        # 下采样配置
        threshold_input = input("电流阈值 (例如: 1): ").strip()
        if threshold_input:
            try:
                threshold = float(threshold_input)
                if 'downsample_params' not in custom_config:
                    custom_config['downsample_params'] = {
                        'i_threshold': threshold,
                        'factor_high': 5,
                        'factor_low': 3
                    }
            except:
                print("阈值格式错误，使用默认值")
    
    # 开始批量处理
    print(f"\n开始批量处理...")
    batch_process_battery_data(input_path, custom_config)
    
    print(f"\n批量处理完成! 请检查输出目录中的图片文件。")

if __name__ == "__main__":
    main()
