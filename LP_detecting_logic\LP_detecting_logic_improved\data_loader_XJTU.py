"""
析锂检测数据读取模块
专为基于物理先验的Transformer析锂检测技术方案设计
支持CV段识别、物理软指标计算和实时流式处理
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Iterator
from pathlib import Path
from collections import deque
import time
import warnings
from scipy import signal
from scipy.interpolate import interp1d
from sklearn.mixture import GaussianMixture
import numba


# 自定义异常类
class LithiumDetectionError:
    """析锂检测专用异常类"""

    class CVSegmentNotFound(Exception):
        """CV段未找到异常"""

        pass

    class PhysicsConstraintViolation(Exception):
        """物理约束违反异常"""

        def __init__(self, constraint_name: str, message: str):
            self.constraint_name = constraint_name
            super().__init__(message)

    class SoftMetricsCalculationError(Exception):
        """软指标计算异常"""

        def __init__(self, metric_name: str, invalid_value: float, message: str):
            self.metric_name = metric_name
            self.invalid_value = invalid_value
            super().__init__(message)


class LithiumDetectionDataLoader:
    """专为析锂检测设计的电池数据加载器"""

    def __init__(self, config: Dict):
        """
        初始化析锂检测数据加载器

        Args:
            config: 配置参数字典，包含CV检测、物理计算、实时处理等参数
        """
        self.config = config
        self.sampling_rate = config.get("sampling_rate", 1.0)

        # CV段识别参数
        self.cv_params = config.get(
            "cv_detection_params",
            {
                "v_var_threshold": 1e-4,
                "i_slope_threshold": -1e-5,
                "min_duration": 30,
                "adaptive_quantile": 0.1,
            },
        )

        # 物理软指标参数
        self.physics_params = config.get(
            "physics_params",
            {
                "tau_slope": 0.002,
                "kappa_pos": 0.0005,
                "kappa_neg": -0.0005,
                "alpha_baseline": 0.98,
                "sigma_p": 0.001,
                "sigma_b": 0.001,
                "i_threshold": 0.1,
            },
        )

        # 实时处理参数
        self.realtime_params = config.get(
            "realtime_params",
            {
                "window_size": 30,
                "step_size": 5,
                "physics_activation_threshold": 0.3,
                "buffer_size": 1000,
            },
        )

        # 初始化缓存
        self.cv_cache = {}
        self.metrics_cache = {}
        self.realtime_buffer = deque(maxlen=self.realtime_params["buffer_size"])

    def load_with_cv_detection(
        self, file_path: str
    ) -> Dict[str, Union[np.ndarray, List]]:
        """
        加载数据并自动识别CV段

        Args:
            file_path: 数据文件路径

        Returns:
            包含原始数据、CV段边界和质量评分的字典
        """
        # 加载原始数据
        raw_data = self._load_raw_data(file_path)

        # 数据预处理
        processed_data = self._preprocess_data(raw_data)

        # CV段识别
        cv_segments = self._detect_cv_segments(processed_data)

        if not cv_segments:
            raise LithiumDetectionError.CVSegmentNotFound("未检测到有效的CV段")

        # CV段质量评估
        cv_quality = self._assess_cv_quality(cv_segments, processed_data)

        return {
            "raw_data": raw_data,
            "processed_data": processed_data,
            "cv_segments": cv_segments,
            "cv_quality": cv_quality,
        }

    def compute_physics_soft_metrics(self, cv_data: Dict) -> Dict[str, float]:
        """
        计算7维物理软指标向量

        Args:
            cv_data: CV段数据

        Returns:
            7维软指标字典
        """
        try:
            t = cv_data["t"]
            I = cv_data["I"]
            V = cv_data["V"]
            Q = cv_data["Q"]

            # 数据平滑处理
            I_smooth = self._smooth_current(I, t)

            # 计算一阶和二阶导数
            dI_dt = np.gradient(I_smooth, t)
            d2I_dt2 = np.gradient(dI_dt, t)

            # 基线估计
            I_baseline = self._estimate_baseline(I_smooth, t)

            # 计算7个软指标
            S_plat = self._compute_platform_metric(dI_dt)
            S_bump = self._compute_bump_metric(d2I_dt2)
            S_mono = self._compute_monotonicity_metric(I_smooth, I_baseline)
            S_amp = self._compute_amplitude_metric(I_smooth, I_baseline, t)
            Q_tilde = self._compute_lithium_proxy(Q, I_smooth)
            C_low = self._compute_low_current_consistency(I_smooth, t)
            C_kappa = self._compute_curvature_consistency(d2I_dt2)

            metrics = {
                "S_plat": S_plat,
                "S_bump": S_bump,
                "S_mono": S_mono,
                "S_amp": S_amp,
                "Q_tilde": Q_tilde,
                "C_low": C_low,
                "C_kappa": C_kappa,
            }

            # 验证指标合理性
            self._validate_metrics(metrics)

            return metrics

        except Exception as e:
            raise LithiumDetectionError.SoftMetricsCalculationError(
                "unknown", 0.0, f"软指标计算失败: {str(e)}"
            )

    def create_sliding_windows(
        self, data: Dict, window_size: int, step_size: int
    ) -> Iterator:
        """
        创建滑窗数据流用于实时检测

        Args:
            data: 时序数据
            window_size: 窗口大小（秒）
            step_size: 步长（秒）

        Returns:
            滑窗数据迭代器
        """
        t = data["t"]
        I = data["I"]
        V = data["V"]
        Q = data["Q"]

        # 转换为采样点数
        window_points = int(window_size * self.sampling_rate)
        step_points = int(step_size * self.sampling_rate)

        for start_idx in range(0, len(t) - window_points + 1, step_points):
            end_idx = start_idx + window_points

            window_data = {
                "t": t[start_idx:end_idx],
                "I": I[start_idx:end_idx],
                "V": V[start_idx:end_idx],
                "Q": Q[start_idx:end_idx],
            }

            yield window_data

    def generate_weak_supervision_labels(
        self, physics_metrics: Dict
    ) -> Dict[str, float]:
        """
        基于物理软指标生成弱监督标签

        Args:
            physics_metrics: 物理软指标字典

        Returns:
            包含软标签和硬标签的字典
        """
        # 重要指标权重
        weights = {
            "S_amp": 1.0,
            "S_plat": 1.0,
            "C_low": 1.0,
            "S_bump": 0.8,
            "S_mono": 0.5,
            "C_kappa": 0.5,
            "Q_tilde": 0.5,
        }

        # 加权融合得到软标签
        weighted_sum = sum(
            physics_metrics[key] * weights[key] for key in weights.keys()
        )
        total_weight = sum(weights.values())
        soft_label = weighted_sum / total_weight

        # 基于软标签生成硬标签
        hard_label = 1 if soft_label > 0.5 else 0

        # 置信度计算
        confidence = abs(soft_label - 0.5) * 2  # 距离0.5越远置信度越高

        return {
            "soft_label": soft_label,
            "hard_label": hard_label,
            "confidence": confidence,
        }

    # ======================
    # 核心私有方法实现
    # ======================

    def _load_raw_data(self, file_path: str) -> Dict[str, np.ndarray]:
        """加载原始数据"""
        file_path = Path(file_path)

        if file_path.suffix.lower() == ".csv":
            df = pd.read_csv(file_path)
        elif file_path.suffix.lower() in [".xlsx", ".xls"]:
            df = pd.read_excel(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_path.suffix}")

        # 检查必需列
        required_cols = ["t", "I", "V", "Q"]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"缺少必需列: {missing_cols}")

        return {
            "t": df["t"].values,
            "I": df["I"].values,
            "V": df["V"].values,
            "Q": df["Q"].values,
        }

    def _detect_cv_segments(self, data: Dict) -> List[Tuple[int, int]]:
        """CV段识别 - 双重门控策略"""
        t, I, V, Q = data["t"], data["I"], data["V"], data["Q"]

        # 电压方差门控
        window_size = int(30 * self.sampling_rate)  # 30秒窗口
        v_var_threshold = self.cv_params["v_var_threshold"]

        voltage_variance = []
        for i in range(len(V) - window_size + 1):
            window_var = np.var(V[i : i + window_size])
            voltage_variance.append(window_var)

        # 电流斜率门控
        dI_dt = np.gradient(I, t)
        i_slope_threshold = self.cv_params["i_slope_threshold"]

        # 组合判断
        cv_mask = np.zeros(len(V), dtype=bool)
        for i in range(len(voltage_variance)):
            if (
                voltage_variance[i] < v_var_threshold
                and dI_dt[i] < i_slope_threshold
                and I[i] > 0
            ):  # 确保电流为正
                cv_mask[i : i + window_size] = True

        # 提取连续段
        cv_segments = []
        in_segment = False
        start_idx = 0

        min_duration = self.cv_params["min_duration"]
        min_points = int(min_duration * self.sampling_rate)

        for i, is_cv in enumerate(cv_mask):
            if is_cv and not in_segment:
                start_idx = i
                in_segment = True
            elif not is_cv and in_segment:
                if i - start_idx >= min_points:
                    cv_segments.append((start_idx, i))
                in_segment = False

        # 处理末尾情况
        if in_segment and len(cv_mask) - start_idx >= min_points:
            cv_segments.append((start_idx, len(cv_mask)))

        return cv_segments

    @numba.jit(nopython=True)
    def _compute_platform_metric(self, dI_dt: np.ndarray) -> float:
        """计算平台度指标 S_plat"""
        tau = self.physics_params["tau_slope"]
        sigma_p = self.physics_params["sigma_p"]

        N = len(dI_dt)
        platform_sum = 0.0

        for i in range(N):
            sigmoid_arg = (tau - abs(dI_dt[i])) / sigma_p
            # 避免数值溢出
            if sigmoid_arg > 500:
                platform_sum += 1.0
            elif sigmoid_arg < -500:
                platform_sum += 0.0
            else:
                platform_sum += 1.0 / (1.0 + np.exp(-sigmoid_arg))

        return platform_sum / N

    def _compute_bump_metric(self, d2I_dt2: np.ndarray) -> float:
        """计算凸起度指标 S_bump"""
        kappa_pos = self.physics_params["kappa_pos"]
        kappa_neg = self.physics_params["kappa_neg"]
        sigma_b = self.physics_params["sigma_b"]
        eta = 0.001  # 阈值参数

        # 计算正负曲率区域
        A_pos = np.sum(np.maximum(0, d2I_dt2 - kappa_pos))
        A_neg = np.sum(np.maximum(0, -kappa_neg - d2I_dt2))

        # 凸起指标
        bump_product = A_pos * A_neg - eta
        sigmoid_arg = bump_product / sigma_b

        if sigmoid_arg > 500:
            return 1.0
        elif sigmoid_arg < -500:
            return 0.0
        else:
            return 1.0 / (1.0 + np.exp(-sigmoid_arg))

    def _compute_amplitude_metric(
        self, I_smooth: np.ndarray, I_baseline: np.ndarray, t: np.ndarray
    ) -> float:
        """计算回嵌幅度指标 S_amp"""
        i_threshold = self.physics_params["i_threshold"]
        sigma_i = 0.05  # 电流权重参数

        # 计算电流偏差
        delta_I_pos = np.maximum(0, I_smooth - I_baseline)

        # 低电流权重
        w_low = 1.0 / (1.0 + np.exp((np.abs(I_smooth) - i_threshold) / sigma_i))

        # 加权平均
        weighted_sum = np.sum(w_low * delta_I_pos)
        total_weight = np.sum(w_low)

        return weighted_sum / total_weight if total_weight > 0 else 0.0

    def load_realtime_stream(self, bms_config: Dict) -> Dict[str, np.ndarray]:
        """
        从BMS实时数据流加载数据

        Args:
            bms_config: BMS连接配置

        Returns:
            实时数据流
        """
        pass

    def validate_data_format(self, data: Dict[str, np.ndarray]) -> bool:
        """
        验证数据格式的完整性和正确性

        Args:
            data: 待验证的数据字典

        Returns:
            验证结果布尔值
        """
        pass

    def resample_data(
        self, data: Dict[str, np.ndarray], target_freq: float
    ) -> Dict[str, np.ndarray]:
        """
        数据重采样到目标频率

        Args:
            data: 原始数据
            target_freq: 目标采样频率(Hz)

        Returns:
            重采样后的数据
        """
        pass

    def split_cycles(self, data: Dict[str, np.ndarray]) -> List[Dict[str, np.ndarray]]:
        """
        将连续数据按充放电循环切分

        Args:
            data: 连续的电池数据

        Returns:
            按循环切分的数据列表
        """
        pass

    def filter_noise(
        self, data: Dict[str, np.ndarray], filter_params: Dict
    ) -> Dict[str, np.ndarray]:
        """
        对数据进行噪声滤波

        Args:
            data: 原始数据
            filter_params: 滤波参数

        Returns:
            滤波后的数据
        """
        pass


class LithiumDataValidator:
    """专为析锂检测优化的数据质量验证器"""

    def __init__(self, config: Dict = None):
        """初始化验证器"""
        self.config = config or {}

        # 析锂检测物理约束
        self.lithium_constraints = self.config.get(
            "lithium_constraints",
            {
                "voltage_range": [3.0, 4.2],
                "current_range": [0.001, 10],
                "voltage_stability": 1e-4,
                "current_monotonicity": -1e-6,
                "min_cv_points": 20,
            },
        )

        # 软指标验证范围
        self.metrics_ranges = self.config.get(
            "metrics_ranges",
            {
                "S_plat": [0, 1],
                "S_bump": [0, 1],
                "S_mono": [0, 1],
                "S_amp": [0, 1],
                "Q_tilde": [0, float("inf")],
                "C_low": [0, 1],
                "C_kappa": [0, 1],
            },
        )

    def validate_cv_segment_quality(self, cv_segments: List[Dict]) -> Dict[str, float]:
        """
        验证CV段数据质量

        Args:
            cv_segments: CV段数据列表

        Returns:
            质量评分字典
        """
        if not cv_segments:
            return {"overall_score": 0.0, "segment_count": 0}

        quality_scores = []

        for segment in cv_segments:
            V = segment.get("V", [])
            I = segment.get("I", [])
            t = segment.get("t", [])

            if len(V) < self.lithium_constraints["min_cv_points"]:
                continue

            # 电压稳定性评分
            voltage_stability = 1.0 - min(
                1.0, np.var(V) / self.lithium_constraints["voltage_stability"]
            )

            # 电流单调性评分
            dI_dt = np.gradient(I, t)
            monotonicity_score = np.sum(dI_dt < 0) / len(dI_dt)

            # 段长度评分
            duration = t[-1] - t[0]
            length_score = min(1.0, duration / 60.0)  # 60秒为满分

            # 综合评分
            segment_score = (
                voltage_stability * 0.4 + monotonicity_score * 0.4 + length_score * 0.2
            )

            quality_scores.append(segment_score)

        return {
            "overall_score": np.mean(quality_scores) if quality_scores else 0.0,
            "segment_count": len(quality_scores),
            "segment_scores": quality_scores,
        }

    def check_lithium_physics_constraints(
        self, data: Dict[str, np.ndarray]
    ) -> Dict[str, bool]:
        """
        验证析锂检测的物理约束

        Args:
            data: 电池数据

        Returns:
            物理约束检查结果
        """
        V = data["V"]
        I = data["I"]
        Q = data["Q"]
        t = data["t"]

        constraints_check = {}

        # 电压范围检查
        v_min, v_max = self.lithium_constraints["voltage_range"]
        constraints_check["voltage_range"] = np.all((V >= v_min) & (V <= v_max))

        # 电流范围检查
        i_min, i_max = self.lithium_constraints["current_range"]
        constraints_check["current_range"] = np.all((I >= i_min) & (I <= i_max))

        # 电流正值约束（CV段）
        constraints_check["current_positive"] = np.all(I > 0)

        # 容量单调性检查
        dQ_dt = np.gradient(Q, t)
        constraints_check["capacity_monotonic"] = np.all(dQ_dt >= 0)

        # 电流单调衰减检查
        dI_dt = np.gradient(I, t)
        monotonicity_threshold = self.lithium_constraints["current_monotonicity"]
        constraints_check["current_decreasing"] = np.all(
            dI_dt <= monotonicity_threshold
        )

        return constraints_check

    def validate_soft_metrics_range(self, metrics: Dict[str, float]) -> Dict[str, bool]:
        """
        验证7维软指标的合理性

        Args:
            metrics: 物理软指标字典

        Returns:
            各指标的合理性检查结果
        """
        validation_results = {}

        for metric_name, value in metrics.items():
            if metric_name in self.metrics_ranges:
                min_val, max_val = self.metrics_ranges[metric_name]
                validation_results[metric_name] = min_val <= value <= max_val
            else:
                validation_results[metric_name] = True  # 未知指标默认通过

        # 额外的逻辑检查
        # 检查指标间的逻辑一致性
        if "S_plat" in metrics and "S_bump" in metrics:
            # 平台和凸起通常不会同时很高
            if metrics["S_plat"] > 0.8 and metrics["S_bump"] > 0.8:
                validation_results["logical_consistency"] = False
            else:
                validation_results["logical_consistency"] = True

        return validation_results

    def detect_outliers(self, data: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        检测数据中的异常值

        Args:
            data: 待检测的数据

        Returns:
            异常值位置索引
        """
        pass

    def check_sampling_consistency(self, time_series: np.ndarray) -> Dict[str, float]:
        """
        检查时间序列采样一致性

        Args:
            time_series: 时间序列数据

        Returns:
            采样统计信息
        """
        pass

    def validate_physical_constraints(self, data: Dict[str, np.ndarray]) -> List[str]:
        """
        验证物理约束条件

        Args:
            data: 电池数据

        Returns:
            违反约束的警告信息列表
        """
        pass


class PhysicsMetricsCache:
    """专为析锂检测优化的数据缓存管理器"""

    def __init__(self, cache_size: int = 1000):
        """
        初始化缓存管理器

        Args:
            cache_size: 缓存大小限制
        """
        self.cache_size = cache_size

        # 多级缓存
        self.cv_segments_cache = {}  # CV段缓存
        self.soft_metrics_cache = {}  # 软指标缓存
        self.physics_validation_cache = {}  # 物理验证结果缓存

        # 访问统计
        self.access_count = {}
        self.hit_count = 0
        self.miss_count = 0

        # LRU链表
        from collections import OrderedDict

        self.lru_tracker = OrderedDict()

    def cache_cv_segments(
        self, data_hash: str, cv_segments: List, quality_score: Dict
    ) -> bool:
        """缓存CV段识别结果"""
        if len(self.cv_segments_cache) >= self.cache_size:
            self._evict_lru("cv_segments")

        cache_entry = {
            "cv_segments": cv_segments,
            "quality_score": quality_score,
            "timestamp": time.time(),
        }

        self.cv_segments_cache[data_hash] = cache_entry
        self.lru_tracker[f"cv_{data_hash}"] = time.time()
        return True

    def cache_soft_metrics(self, segment_hash: str, metrics: Dict[str, float]) -> bool:
        """缓存物理软指标计算结果"""
        if len(self.soft_metrics_cache) >= self.cache_size:
            self._evict_lru("soft_metrics")

        cache_entry = {"metrics": metrics, "timestamp": time.time()}

        self.soft_metrics_cache[segment_hash] = cache_entry
        self.lru_tracker[f"metrics_{segment_hash}"] = time.time()
        return True

    def get_cached_cv_segments(self, data_hash: str) -> Optional[Dict]:
        """获取缓存的CV段"""
        if data_hash in self.cv_segments_cache:
            self.hit_count += 1
            self.lru_tracker[f"cv_{data_hash}"] = time.time()
            return self.cv_segments_cache[data_hash]
        else:
            self.miss_count += 1
            return None

    def get_cached_metrics(self, segment_hash: str) -> Optional[Dict]:
        """获取缓存的软指标"""
        if segment_hash in self.soft_metrics_cache:
            self.hit_count += 1
            self.lru_tracker[f"metrics_{segment_hash}"] = time.time()
            return self.soft_metrics_cache[segment_hash]
        else:
            self.miss_count += 1
            return None

    def _evict_lru(self, cache_type: str) -> None:
        """LRU淘汰策略"""
        if cache_type == "cv_segments" and self.cv_segments_cache:
            # 找到最久未使用的CV段缓存
            oldest_key = None
            oldest_time = float("inf")

            for key in self.cv_segments_cache:
                lru_key = f"cv_{key}"
                if lru_key in self.lru_tracker:
                    if self.lru_tracker[lru_key] < oldest_time:
                        oldest_time = self.lru_tracker[lru_key]
                        oldest_key = key

            if oldest_key:
                del self.cv_segments_cache[oldest_key]
                del self.lru_tracker[f"cv_{oldest_key}"]

    def get_cache_stats(self) -> Dict[str, Union[int, float]]:
        """获取缓存统计信息"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0.0

        return {
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": hit_rate,
            "cv_cache_size": len(self.cv_segments_cache),
            "metrics_cache_size": len(self.soft_metrics_cache),
            "total_cache_size": len(self.cv_segments_cache)
            + len(self.soft_metrics_cache),
        }

    def cache_data(self, key: str, data: Dict[str, np.ndarray]) -> bool:
        """
        缓存数据到内存

        Args:
            key: 缓存键
            data: 待缓存的数据

        Returns:
            缓存是否成功
        """
        pass

    def get_cached_data(self, key: str) -> Optional[Dict[str, np.ndarray]]:
        """
        从缓存获取数据

        Args:
            key: 缓存键

        Returns:
            缓存的数据或None
        """
        pass

    def clear_cache(self) -> None:
        """清空所有缓存"""
        pass

    def get_cache_stats(self) -> Dict[str, int]:
        """
        获取缓存统计信息

        Returns:
            缓存统计字典
        """
        pass
