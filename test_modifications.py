"""
测试修改后的电池分析工具
Test the modified battery analysis tool
"""

from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)
import json
import numpy as np
import pandas as pd

def create_test_data():
    """创建测试数据"""
    log("Creating test data...")
    
    # 生成测试数据
    test_data = {}
    
    # 生成5个循环的示例数据
    for cycle in range(1, 6):
        # 生成时间数据 (分钟)
        time_points = 50
        time_min = np.linspace(0, 1, time_points)  # 1分钟的测试时间
        
        # 生成电流数据 (模拟放电曲线)
        current_A = []
        for t in time_min:
            if t < 0.1:  # 初始阶段
                current = -1.0
            elif t < 0.8:  # 主要放电阶段
                current = -1.0 + 0.1 * np.sin(t * 20) * np.exp(-t/0.5)
            else:  # 结束阶段
                current = -0.1 * (1 - t)
            
            # 添加一些噪声和循环差异
            current += np.random.normal(0, 0.01)
            current *= (1 - 0.01 * (cycle - 1))  # 模拟性能衰减
            current_A.append(current)
        
        # 存储循环数据
        test_data[f"Cycle_{cycle}"] = {
            "relative_time_min": time_min.tolist(),
            "current_A": current_A
        }
    
    # 生成容量数据 (模拟容量衰减)
    capacities = []
    for cycle in range(1, 6):
        capacity = 1.0 - 0.05 * (cycle - 1) - 0.01 * (cycle - 1)**2  # 模拟衰减
        capacities.append(capacity)
    
    test_data["Discharge Capacity"] = capacities
    
    # 生成库伦效率数据
    ce_values = []
    for cycle in range(1, 6):
        ce = 0.99 - 0.001 * (cycle - 1) + 0.005 * np.random.random()  # 模拟CE值
        ce_values.append(ce)
    
    test_data["CE"] = ce_values
    
    # 保存到文件
    filename = "test_battery_data.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2)
    
    log(f"Test data file created: {filename}")
    return filename

def test_analysis_with_modifications():
    """测试修改后的分析功能"""
    
    # 创建测试数据
    test_file = create_test_data()
    
    try:
        log("="*60)
        log("Testing modified battery analysis tool...")
        log("="*60)
        
        # 步骤1: 提取数据
        log("Step 1: Extracting data...")
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(test_file)
        
        if all_cycles_data is None:
            log("Error: Failed to extract data")
            return False
        
        # 步骤2: 计算dI/dt
        log("Step 2: Calculating dI/dt...")
        didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_factor=1)
        
        # 步骤3: 检测膝点
        log("Step 3: Detecting knee points...")
        knee_points = detect_knee_points(cycle_capacities)
        
        # 步骤4: 测试不同的参数组合
        test_cases = [
            {
                "name": "Default settings",
                "Kcls": [1, 3, 5],
                "current_ylim": None,
                "didt_ylim": None,
                "save_path": "test_default.png"
            },
            {
                "name": "Custom Y-axis ranges",
                "Kcls": [2, 4],
                "current_ylim": (-1.2, 0.2),
                "didt_ylim": (-0.05, 0.01),
                "save_path": "test_custom_ylim.png"
            },
            {
                "name": "No marked cycles",
                "Kcls": None,
                "current_ylim": None,
                "didt_ylim": None,
                "save_path": "test_no_marks.png"
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            log(f"Step 4.{i+1}: Testing {test_case['name']}...")
            
            fig = create_battery_analysis_plots(
                all_cycles_data=all_cycles_data,
                didt_cycles_data=didt_cycles_data,
                cycle_capacities=cycle_capacities,
                cycle_ce_values=cycle_ce_values,
                knee_points=knee_points,
                Kcls=test_case['Kcls'],
                sample_name=f"Test_{test_case['name'].replace(' ', '_')}",
                figsize=(12, 10),
                dpi=100,
                save_path=test_case['save_path'],
                current_ylim=test_case['current_ylim'],
                didt_ylim=test_case['didt_ylim'],
            )
            
            log(f"  ✓ {test_case['name']} completed successfully")
        
        # 输出测试总结
        log("="*60)
        log("Test completed successfully!")
        log("="*60)
        log("Test Summary:")
        log(f"  - Total cycles processed: {len(all_cycles_data)}")
        log(f"  - Cycles with dI/dt data: {len(didt_cycles_data)}")
        log(f"  - Cycles with capacity data: {len(cycle_capacities)}")
        log(f"  - Cycles with CE data: {len(cycle_ce_values) if cycle_ce_values else 0}")
        log(f"  - Auto-detected knee points: {knee_points}")
        log(f"  - Test cases completed: {len(test_cases)}")
        log("="*60)
        
        # 验证新功能
        log("Verification of new features:")
        log("  ✓ Gradient colors (viridis) for current and dI/dt plots")
        log("  ✓ Dual Y-axis for capacity (left) and CE (right)")
        log("  ✓ Capacity change rate plot instead of knee point analysis")
        log("  ✓ Customizable Y-axis ranges for current and dI/dt plots")
        log("  ✓ Black marking for selected cycles (Kcls)")
        
        return True
        
    except Exception as e:
        log(f"Error during testing: {str(e)}")
        import traceback
        log(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("Modified Battery Analysis Tool - Test Suite")
    print("="*60)
    
    success = test_analysis_with_modifications()
    
    if success:
        print("\n✓ All tests passed successfully!")
        print("Check the generated PNG files to verify the visual improvements:")
        print("  - test_default.png")
        print("  - test_custom_ylim.png") 
        print("  - test_no_marks.png")
    else:
        print("\n✗ Tests failed. Check the log for details.")

if __name__ == "__main__":
    main()
