"""
拖拽式批量电池数据分析脚本
Drag & Drop Batch Battery Data Analysis Script

支持拖拽文件夹或文件到脚本上直接运行
Support dragging folders or files to the script for direct execution
"""

import os
import glob
import sys
from pathlib import Path
from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)

def get_sample_name(json_file_path):
    """提取样品名称"""
    return Path(json_file_path).stem.replace('_CV_Qcha_CE', '').replace('_data', '')

def process_with_current_settings(json_file_path, output_dir):
    """使用当前设置处理文件"""
    sample_name = get_sample_name(json_file_path)
    output_path = os.path.join(output_dir, f"{sample_name}_analysis.png")
    
    try:
        # 提取数据
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(json_file_path)
        if not all_cycles_data:
            return False, "数据提取失败"
        
        # 处理数据
        didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_params={
            'i_threshold': 1, 'factor_high': 5, 'factor_low': 3
        })
        knee_points = detect_knee_points(cycle_capacities)
        
        # 生成图表
        create_battery_analysis_plots(
            all_cycles_data=all_cycles_data,
            didt_cycles_data=didt_cycles_data,
            cycle_capacities=cycle_capacities,
            cycle_ce_values=cycle_ce_values,
            knee_points=knee_points,
            Kcls=[2, 20, 50, 150],
            sample_name=sample_name,
            figsize=(15, 12),
            dpi=330,
            save_path=output_path,
            current_ylim=(0, 2),
            didt_ylim=(-0.006, 0.001),
            capacity_ylim=None,
            ce_ylim=(0.98, 1.01),
            change_rate_ylim=None,
            colormap='Blues'
        )
        
        return True, f"成功 - 循环数:{len(all_cycles_data)}"
        
    except Exception as e:
        return False, f"错误: {str(e)}"

def batch_process_path(input_path):
    """批量处理路径"""
    
    # 获取文件列表
    if os.path.isfile(input_path) and input_path.endswith('.json'):
        json_files = [input_path]
        output_dir = os.path.dirname(input_path)
    elif os.path.isdir(input_path):
        json_files = glob.glob(os.path.join(input_path, "*.json"))
        output_dir = input_path
    else:
        print(f"❌ 无效路径: {input_path}")
        return
    
    if not json_files:
        print(f"❌ 未找到JSON文件: {input_path}")
        return
    
    print(f"📁 找到 {len(json_files)} 个JSON文件")
    print(f"📂 输出目录: {output_dir}")
    print("🔄 开始处理...")
    print("-" * 60)
    
    # 处理文件
    results = []
    for i, json_file in enumerate(json_files, 1):
        filename = Path(json_file).name
        print(f"[{i:2d}/{len(json_files)}] {filename[:40]:<40} ", end="")
        
        success, message = process_with_current_settings(json_file, output_dir)
        
        if success:
            print(f"✅ {message}")
            results.append(True)
        else:
            print(f"❌ {message}")
            results.append(False)
    
    # 统计结果
    successful = sum(results)
    total = len(results)
    
    print("-" * 60)
    print(f"🎉 处理完成!")
    print(f"📊 成功: {successful}/{total} ({successful/total*100:.1f}%)")
    print(f"💾 图片保存在: {output_dir}")
    
    if successful < total:
        print(f"⚠️  有 {total-successful} 个文件处理失败")

def main():
    """主函数"""
    print("🔋 拖拽式批量电池数据分析工具")
    print("=" * 60)
    
    # 检查拖拽参数
    if len(sys.argv) > 1:
        # 拖拽模式
        for path in sys.argv[1:]:
            print(f"📥 处理拖拽路径: {path}")
            batch_process_path(path)
            print()
    else:
        # 交互模式
        print("💡 使用方法:")
        print("   1. 直接拖拽文件夹或JSON文件到此脚本")
        print("   2. 或者手动输入路径")
        print()
        
        while True:
            path = input("📝 请输入路径 (或按Enter退出): ").strip().strip('"')
            if not path:
                break
            
            if os.path.exists(path):
                batch_process_path(path)
                print()
            else:
                print(f"❌ 路径不存在: {path}")
    
    print("👋 程序结束")
    input("按Enter键退出...")

if __name__ == "__main__":
    main()
