import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import json
import matplotlib.cm as cm
from scipy.signal import savgol_filter
from scipy.interpolate import interp1d
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format="[%(asctime)s] %(message)s")


def log(message):
    """Record log messages"""
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {message}")
    logging.info(message)


def compute_dIdt_vectorized(time_s, current_A, internal=50):
    """Calculate dI/dt using vectorized method for better performance"""
    if len(time_s) < 2:
        return None, None

    try:
        # Convert to numpy arrays for vector operations
        time_array = np.array(time_s)
        current_array = np.array(current_A)

        # Calculate time differences and current differences
        dt = np.diff(time_array)
        dI = np.diff(current_array)

        # 大幅降低阈值，使更多数据点被保留
        # Calculate threshold - 使用更低的internal值来降低阈值
        max_current = np.max(np.abs(current_array))
        threshold = max_current / (internal * 10)  # 将阈值降低10倍

        # 只过滤掉时间差无效的点
        valid_mask = np.abs(dt) > 1e-10

        # 计算所有有效点的dI/dt
        dIdt_values = dI[valid_mask] / dt[valid_mask]
        didt_times = time_array[:-1][valid_mask]

        if len(dIdt_values) == 0:
            return None, None

        # Fix: Ensure we're not returning NaN or infinite values
        valid_values = ~np.isnan(dIdt_values) & ~np.isinf(dIdt_values)
        if not np.all(valid_values):
            dIdt_values = dIdt_values[valid_values]
            didt_times = didt_times[valid_values]

        if len(dIdt_values) == 0:
            return None, None

        # 对dI/dt值进行平滑处理，减少噪声
        if len(dIdt_values) > 5:
            window_size = min(5, len(dIdt_values) - (len(dIdt_values) % 2 == 0))
            if window_size >= 3:
                try:
                    dIdt_values = savgol_filter(dIdt_values, window_size, 2)
                except Exception:
                    pass  # 如果平滑失败，使用原始值

        # 确保返回的是numpy数组而不是视图
        return np.array(dIdt_values), np.array(didt_times)
    except Exception as e:
        log(f"Error in compute_dIdt_vectorized: {str(e)}")
        return None, None


def process_cycle(cycle_data):
    """Process a single cycle to calculate dI/dt"""
    cycle_num, data, downsample_params = cycle_data

    # Apply adaptive downsampling based on current threshold
    if isinstance(downsample_params, dict):
        # Adaptive downsampling based on current magnitude
        i_threshold = downsample_params.get("i_threshold", 0.5)
        factor_high = downsample_params.get(
            "factor_high", 1
        )  # For |current| > threshold
        factor_low = downsample_params.get(
            "factor_low", 1
        )  # For |current| <= threshold

        # Split data based on current threshold
        current_abs = np.abs(data["Current(A)"])
        high_current_mask = current_abs > i_threshold
        low_current_mask = current_abs <= i_threshold

        # Apply different downsampling factors
        processed_data_parts = []

        if high_current_mask.any() and factor_high > 1:
            high_current_data = data[high_current_mask]
            high_indices = np.arange(0, len(high_current_data), factor_high)
            if len(high_indices) > 0:
                high_current_data = high_current_data.iloc[high_indices].copy()
            processed_data_parts.append(high_current_data)
        elif high_current_mask.any():
            processed_data_parts.append(data[high_current_mask])

        if low_current_mask.any() and factor_low > 1:
            low_current_data = data[low_current_mask]
            low_indices = np.arange(0, len(low_current_data), factor_low)
            if len(low_indices) > 0:
                low_current_data = low_current_data.iloc[low_indices].copy()
            processed_data_parts.append(low_current_data)
        elif low_current_mask.any():
            processed_data_parts.append(data[low_current_mask])

        # Combine and sort by time
        if processed_data_parts:
            data = pd.concat(processed_data_parts).sort_values(by="Time(s)")

    else:
        # Traditional uniform downsampling
        downsample_factor = downsample_params
        if downsample_factor > 1:
            indices = np.arange(0, len(data), downsample_factor)
            if len(indices) > 0:
                data = data.iloc[indices].copy()

    # Extract time and current data
    time_s = data["Time(s)"]  # 这里已经是秒为单位的时间
    current_A = data["Current(A)"]

    # 简化调试信息，只在第一个循环或特定循环输出
    if cycle_num <= 3 or cycle_num % 50 == 0:
        print(
            f"Processing cycle {cycle_num}: {len(data)} data points, time range: {time_s.min():.1f}s - {time_s.max():.1f}s"
        )

    # Calculate dI/dt
    didt_values, didt_times = compute_dIdt_vectorized(time_s, current_A)

    if didt_values is not None and didt_times is not None:
        # 只在第一个循环或特定循环输出调试信息
        if cycle_num <= 3 or cycle_num % 50 == 0:
            print(
                f"  Cycle {cycle_num}: {len(didt_values)} dI/dt points, range: {min(didt_values):.3f} to {max(didt_values):.3f} A/s"
            )

        # 确保时间单位是秒
        # Create dI/dt dataframe
        df_dict = {"Time(s)": didt_times, "dI/dt_raw(A/s)": didt_values}

        # 如果原始数据中有分钟时间，也保存下来
        if "Time(min)" in data.columns:
            # 通过插值找到对应的分钟时间
            # 创建一个从秒到分钟的映射函数
            if len(data["Time(s)"]) > 1 and len(data["Time(min)"]) > 1:
                try:
                    sec_to_min = interp1d(
                        data["Time(s)"].values,
                        data["Time(min)"].values,
                        bounds_error=False,
                        fill_value="extrapolate",
                    )
                    # 计算对应的分钟时间
                    min_times = sec_to_min(didt_times)
                    df_dict["Time(min)"] = min_times
                except Exception as e:
                    if cycle_num <= 3:
                        print(
                            f"  Warning: Could not interpolate minute times for cycle {cycle_num}"
                        )

        # Convert to pandas dataframe
        df_didt = pd.DataFrame(df_dict)

        # Add smoothed dI/dt using rolling window
        df_didt["dI/dt_smooth(A/s)"] = (
            df_didt["dI/dt_raw(A/s)"].rolling(window=5, center=True).mean()
        )

        # Fill NaN values
        df_didt["dI/dt_smooth(A/s)"] = df_didt["dI/dt_smooth(A/s)"].fillna(
            df_didt["dI/dt_raw(A/s)"]
        )

        # 确保数据按时间排序
        df_didt = df_didt.sort_values(by="Time(s)")

        return cycle_num, df_didt
    else:
        if cycle_num <= 10 or cycle_num % 20 == 0:
            print(f"  Warning: No valid dI/dt data for cycle {cycle_num}")
        return cycle_num, None


def process_dIdt_data(all_cycles_data, downsample_params=1):
    """Process all cycle data, calculate dI/dt with optional adaptive downsampling

    Parameters:
    - all_cycles_data: Dictionary of cycle data
    - downsample_params: Can be:
        - int: Traditional uniform downsampling factor
        - dict: Adaptive downsampling parameters with keys:
            - 'i_threshold': Current threshold (A)
            - 'factor_high': Downsampling factor for |current| > threshold
            - 'factor_low': Downsampling factor for |current| <= threshold
    """
    log("Calculating dI/dt for all cycles...")

    # Log downsampling configuration
    if isinstance(downsample_params, dict):
        log(f"Using adaptive downsampling:")
        log(f"  - Current threshold: {downsample_params.get('i_threshold', 0.5)} A")
        log(f"  - High current factor: {downsample_params.get('factor_high', 1)}")
        log(f"  - Low current factor: {downsample_params.get('factor_low', 1)}")
    else:
        log(f"Using uniform downsampling factor: {downsample_params}")

    didt_cycles = {}

    # Create a progress tracking function
    total_cycles = len(all_cycles_data)
    processed_cycles = 0

    for cycle, data in all_cycles_data.items():
        # Process single cycle
        cycle_num, df_didt = process_cycle((cycle, data, downsample_params))

        if df_didt is not None:
            didt_cycles[cycle_num] = df_didt
            log(
                f"  Processed cycle {cycle_num}: calculated {len(df_didt)} dI/dt data points"
            )
        else:
            log(f"  Warning: Unable to calculate dI/dt for cycle {cycle_num}")

        # Update progress
        processed_cycles += 1
        if processed_cycles % 10 == 0 or processed_cycles == total_cycles:
            progress = (processed_cycles / total_cycles) * 100
            log(
                f"  Progress: {progress:.1f}% ({processed_cycles}/{total_cycles} cycles)"
            )

    log(f"dI/dt calculation complete, processed {len(didt_cycles)} cycles")
    return didt_cycles


def detect_knee_points(cycle_capacities, min_pts=3):
    """Automatically detect knee points in capacity data using curvature analysis"""
    if not cycle_capacities or len(cycle_capacities) < min_pts:
        return []

    try:
        # Sort cycles
        cycles = sorted(cycle_capacities.keys())
        capacities = [cycle_capacities[c] for c in cycles]

        if len(cycles) < 5:
            return []  # Not enough data for reliable detection

        # Convert to numpy arrays for numerical operations
        x = np.array(cycles)
        y = np.array(capacities)

        # Apply Savitzky-Golay filter to smooth the data
        if len(x) >= 5:
            window_size = min(
                len(x) - (len(x) % 2 == 0), 5
            )  # Make sure window size is odd and <= len(x)
            if window_size >= 3:
                y_smooth = savgol_filter(
                    y, window_size, 2
                )  # Window size 5, polynomial order 2
            else:
                y_smooth = y
        else:
            y_smooth = y

        # Calculate first and second derivatives
        dy = np.gradient(y_smooth)
        d2y = np.gradient(dy)

        # Calculate curvature: κ = |y''| / (1 + y'^2)^(3/2)
        curvature = np.abs(d2y) / (1 + dy**2) ** 1.5

        # Find local maxima in curvature above threshold (knee points)
        knee_indices = []
        threshold = 0.1 * np.max(curvature)

        # Skip first and last few points to avoid edge effects
        for i in range(2, len(curvature) - 2):
            if (
                curvature[i] > threshold
                and curvature[i] > curvature[i - 1]
                and curvature[i] > curvature[i + 1]
            ):
                knee_indices.append(i)

        # Limit to top 3 knee points
        if knee_indices:
            # Sort by curvature value (descending)
            knee_indices = sorted(
                knee_indices, key=lambda i: curvature[i], reverse=True
            )
            knee_indices = knee_indices[:3]  # Take top 3

        knee_cycles = [cycles[i] for i in knee_indices]
        return knee_cycles

    except Exception as e:
        log(f"Error in knee point detection: {str(e)}")
        return []


def extract_cycles_from_json(json_file_path):
    """Extract cycle data from JSON file with improved error handling"""
    log(f"Loading JSON file: {json_file_path}")

    try:
        with open(json_file_path, "r", encoding="utf-8") as f:
            json_data = json.load(f)
    except Exception as e:
        log(f"Error loading JSON file: {str(e)}")
        return None, None, None

    log("Extracting cycle data from JSON...")

    all_cycles_data = {}
    cycle_capacities = {}
    cycle_ce_values = {}  # New dictionary to store CE values

    # Verify data structure
    if not isinstance(json_data, dict):
        log("Error: Invalid JSON data format - expected a dictionary")
        return None, None, None

    # Extract cycle data from JSON
    for key in json_data.keys():
        if key.startswith("Cycle_"):
            try:
                cycle_num = int(key.split("_")[1])
                cycle_data = json_data[key]

                # Verify required fields exist
                if not isinstance(cycle_data, dict):
                    log(f"  Warning: Invalid data format for cycle {key}")
                    continue

                # Get time data and current data
                time_min = cycle_data.get("relative_time_min", [])
                current_A = cycle_data.get("current_A", [])

                if not time_min or not current_A:
                    log(f"  Warning: Missing time or current data for cycle {key}")
                    continue

                # Check arrays have same length
                if len(time_min) != len(current_A):
                    log(
                        f"  Warning: Time and current arrays have different lengths in cycle {key}"
                    )
                    # Use the shorter length
                    min_len = min(len(time_min), len(current_A))
                    time_min = time_min[:min_len]
                    current_A = current_A[:min_len]

                if len(time_min) > 0 and len(current_A) > 0:
                    # 确保正确转换时间单位：从分钟转换为秒
                    # 首先归一化时间（从0开始）
                    start_time = time_min[0]
                    # 然后将分钟转换为秒
                    time_s = [(t - start_time) * 60 for t in time_min]

                    # Create dataframe
                    df = pd.DataFrame({"Time(s)": time_s, "Current(A)": current_A})
                    # 保存原始分钟数据，以便后续处理
                    df["Time(min)"] = [t - start_time for t in time_min]

                    all_cycles_data[cycle_num] = df
                    log(
                        f"  Extracted cycle {cycle_num}, containing {len(df)} data points"
                    )
            except (ValueError, IndexError, TypeError) as e:
                log(f"  Error processing cycle {key}: {str(e)}")
                continue

    # Extract discharge capacity data (if exists)
    if "Discharge Capacity" in json_data:
        try:
            discharge_capacities = json_data["Discharge Capacity"]
            if isinstance(discharge_capacities, list):
                # Match capacities to cycles
                for i, capacity in enumerate(discharge_capacities):
                    cycle_num = i + 1  # Assuming capacity data starts from cycle 1
                    if (
                        cycle_num in all_cycles_data
                    ):  # Only store capacity for cycles with data
                        # Check if capacity is numeric
                        try:
                            capacity_value = float(capacity)
                            cycle_capacities[cycle_num] = capacity_value
                            log(
                                f"  Extracted capacity for cycle {cycle_num}: {capacity_value:.4f} Ah"
                            )
                        except (ValueError, TypeError):
                            log(
                                f"  Warning: Non-numeric capacity value for cycle {cycle_num}"
                            )
            else:
                log(f"  Warning: 'Discharge Capacity' is not a list")
        except Exception as e:
            log(f"  Error processing capacity data: {str(e)}")
    else:
        log(f"  Note: 'Discharge Capacity' data not found in JSON")

    # Extract CE data (if exists)
    if "CE" in json_data:
        try:
            ce_values = json_data["CE"]
            if isinstance(ce_values, list):
                # Match CE values to cycles
                for i, ce in enumerate(ce_values):
                    cycle_num = i + 1  # Assuming CE data starts from cycle 1
                    if (
                        cycle_num in all_cycles_data
                    ):  # Only store CE for cycles with data
                        # Check if CE is numeric
                        try:
                            ce_value = float(ce)
                            cycle_ce_values[cycle_num] = ce_value
                            log(f"  Extracted CE for cycle {cycle_num}: {ce_value:.6f}")
                        except (ValueError, TypeError):
                            log(
                                f"  Warning: Non-numeric CE value for cycle {cycle_num}"
                            )
            else:
                log(f"  Warning: 'CE' is not a list")
        except Exception as e:
            log(f"  Error processing CE data: {str(e)}")
    else:
        log(f"  Note: 'CE' data not found in JSON")

    log(f"Data extraction complete, {len(all_cycles_data)} cycles found")
    return all_cycles_data, cycle_capacities, cycle_ce_values


def create_battery_analysis_plots(
    all_cycles_data,
    didt_cycles_data,
    cycle_capacities,
    cycle_ce_values=None,
    knee_points=None,
    Kcls=None,
    sample_name="Battery_Analysis",
    figsize=(15, 12),
    dpi=330,
    save_path=None,
    current_ylim=None,
    didt_ylim=None,
    capacity_ylim=None,
    ce_ylim=None,
    change_rate_ylim=None,
    colormap="viridis",
):
    """
    Create comprehensive battery analysis plots with marking functionality

    Parameters:
    - all_cycles_data: Dictionary of cycle data
    - didt_cycles_data: Dictionary of dI/dt data
    - cycle_capacities: Dictionary of capacity data
    - cycle_ce_values: Dictionary of CE values
    - knee_points: List of automatically detected knee points
    - Kcls: List of cycle numbers to mark in black on all plots
    - sample_name: Name for the sample (used in title and filename)
    - figsize: Figure size tuple
    - dpi: Figure DPI
    - save_path: Path to save the figure (optional)
    - current_ylim: Y-axis limits for current plot (tuple: (min, max))
    - didt_ylim: Y-axis limits for dI/dt plot (tuple: (min, max))
    - capacity_ylim: Y-axis limits for capacity plot left axis (tuple: (min, max))
    - ce_ylim: Y-axis limits for CE plot right axis (tuple: (min, max))
    - change_rate_ylim: Y-axis limits for capacity change rate plot (tuple: (min, max))
    - colormap: Colormap name for gradient colors (e.g., 'viridis', 'plasma', 'coolwarm', 'jet')
    """
    log("Creating battery analysis plots...")

    # Create figure with 2x2 subplot layout
    fig, axes = plt.subplots(2, 2, figsize=figsize, dpi=dpi)
    fig.suptitle(f"{sample_name} - Battery Analysis", fontsize=16, y=0.95)

    # Get available cycles for gradient color mapping
    available_cycles = sorted(list(all_cycles_data.keys()))
    n_cycles = len(available_cycles)

    # 使用可调渐变色
    try:
        color_map = cm.get_cmap(colormap, n_cycles)
        colors = [color_map(i) for i in range(n_cycles)]
        log(f"Using colormap: {colormap}")
    except ValueError:
        log(f"Warning: Invalid colormap '{colormap}', falling back to 'viridis'")
        color_map = cm.get_cmap("viridis", n_cycles)
        colors = [color_map(i) for i in range(n_cycles)]

    # Plot 1: Current vs Time (top-left)
    ax_current = axes[0, 0]
    ax_current.set_title("Current vs Time")
    ax_current.set_xlabel("Time (s)")
    ax_current.set_ylabel("Current (A)")
    ax_current.grid(True, alpha=0.3)

    # Plot current data for all cycles with gradient colors
    max_time = 0
    max_current = 0
    for i, cycle_num in enumerate(available_cycles):
        if cycle_num in all_cycles_data and len(all_cycles_data[cycle_num]) > 0:
            cycle_data = all_cycles_data[cycle_num]

            # Downsample if too many points
            if len(cycle_data) > 5000:
                step = len(cycle_data) // 5000
                plot_data = cycle_data.iloc[::step]
            else:
                plot_data = cycle_data

            # Use gradient color from viridis colormap
            color = colors[i]
            alpha = 0.7
            linewidth = 1.0

            # Mark selected cycles in black with thicker lines
            if Kcls and cycle_num in Kcls:
                color = "black"
                alpha = 1.0
                linewidth = 2.0

            ax_current.plot(
                plot_data["Time(s)"],
                plot_data["Current(A)"],
                color=color,
                alpha=alpha,
                linewidth=linewidth,
                label=f"Cycle {cycle_num}" if (Kcls and cycle_num in Kcls) else None,
            )

            max_time = max(max_time, cycle_data["Time(s)"].max())
            max_current = max(max_current, cycle_data["Current(A)"].max())

    # Set y-axis limits for current plot
    if current_ylim:
        ax_current.set_ylim(current_ylim)

    # Add legend for marked cycles
    if Kcls:
        ax_current.legend(loc="best", fontsize=8)

    # Plot 2: dI/dt vs Time (top-right)
    ax_didt = axes[0, 1]
    ax_didt.set_title("dI/dt vs Time")
    ax_didt.set_xlabel("Time (s)")
    ax_didt.set_ylabel("dI/dt (A/s)")
    ax_didt.grid(True, alpha=0.3)

    # Plot dI/dt data for all cycles with gradient colors
    min_didt = 0
    for i, cycle_num in enumerate(available_cycles):
        if (
            cycle_num in didt_cycles_data
            and didt_cycles_data[cycle_num] is not None
            and len(didt_cycles_data[cycle_num]) > 0
        ):
            didt_data = didt_cycles_data[cycle_num]

            # Downsample if too many points
            if len(didt_data) > 1000:
                # Smart downsampling to preserve curve shape
                sorted_data = didt_data.sort_values(by="Time(s)")
                time_range = sorted_data["Time(s)"].max() - sorted_data["Time(s)"].min()
                segments = 20
                segment_size = time_range / segments
                plot_data = []

                for j in range(segments):
                    start_time = sorted_data["Time(s)"].min() + j * segment_size
                    end_time = start_time + segment_size
                    segment_data = sorted_data[
                        (sorted_data["Time(s)"] >= start_time)
                        & (sorted_data["Time(s)"] < end_time)
                    ]

                    if len(segment_data) > 50:
                        indices = np.linspace(0, len(segment_data) - 1, 50, dtype=int)
                        segment_data = segment_data.iloc[indices]

                    plot_data.append(segment_data)

                plot_data = pd.concat(plot_data) if plot_data else didt_data
            else:
                plot_data = didt_data

            # Use gradient color from viridis colormap
            color = colors[i]
            alpha = 0.7
            linewidth = 1.0

            # Mark selected cycles in black with thicker lines
            if Kcls and cycle_num in Kcls:
                color = "black"
                alpha = 1.0
                linewidth = 2.0

            # Use smoothed dI/dt if available
            if "dI/dt_smooth(A/s)" in plot_data.columns:
                time_values = plot_data["Time(s)"]
                didt_values = plot_data["dI/dt_smooth(A/s)"]

                # Filter out invalid values
                valid_mask = (~np.isnan(didt_values)) & (~np.isinf(didt_values))
                if not all(valid_mask):
                    time_values = time_values[valid_mask]
                    didt_values = didt_values[valid_mask]

                ax_didt.plot(
                    time_values,
                    didt_values,
                    color=color,
                    alpha=alpha,
                    linewidth=linewidth,
                    label=(
                        f"Cycle {cycle_num}" if (Kcls and cycle_num in Kcls) else None
                    ),
                )

                min_didt = min(min_didt, didt_values.min())

    # Set y-axis limits for dI/dt plot
    if didt_ylim:
        ax_didt.set_ylim(didt_ylim)

    # Add legend for marked cycles
    if Kcls:
        ax_didt.legend(loc="best", fontsize=8)

    # Plot 3: Capacity vs Cycle (bottom-left) with CE on right y-axis
    ax_capacity = axes[1, 0]
    ax_capacity.set_title("Capacity vs Cycle")
    ax_capacity.set_xlabel("Cycle Number")
    ax_capacity.set_ylabel("Capacity (Ah)", color="blue")
    ax_capacity.grid(True, alpha=0.3)

    # Create twin axis for CE data
    ax_ce = ax_capacity.twinx()
    ax_ce.set_ylabel("Coulombic Efficiency (CE)", color="red")

    if cycle_capacities:
        cycles = sorted(cycle_capacities.keys())
        capacities = [cycle_capacities[c] for c in cycles]

        # Plot capacity curve
        ax_capacity.plot(
            cycles,
            capacities,
            "o-",
            markersize=3,
            linewidth=1.5,
            color="blue",
            label="Capacity",
        )

        # Add trend line (moving average)
        if len(cycles) > 5:
            window = min(5, len(cycles))
            trend = pd.Series(capacities).rolling(window=window, center=True).mean()
            ax_capacity.plot(
                cycles, trend, "b--", linewidth=1.5, alpha=0.7, label="Capacity Trend"
            )

        # Mark knee points if provided
        if knee_points:
            knee_capacities = [cycle_capacities.get(cycle, 0) for cycle in knee_points]
            ax_capacity.plot(
                knee_points,
                knee_capacities,
                "rx",
                markersize=10,
                markeredgewidth=2,
                label="Auto Knee Points",
            )

        # Mark selected cycles in black
        if Kcls:
            marked_cycles = [c for c in Kcls if c in cycle_capacities]
            marked_capacities = [cycle_capacities[c] for c in marked_cycles]
            ax_capacity.plot(
                marked_cycles,
                marked_capacities,
                "ko",
                markersize=8,
                markeredgewidth=2,
                label=f"Selected Cycles {Kcls}",
            )

    # Plot CE data if available
    if cycle_ce_values and len(cycle_ce_values) > 0:
        ce_cycles = sorted(list(cycle_ce_values.keys()))
        ce_values = [cycle_ce_values[c] for c in ce_cycles]

        # Plot CE data with scatter plot
        ax_ce.scatter(
            ce_cycles, ce_values, color="red", marker="o", s=20, alpha=0.7, label="CE"
        )

        # Add connecting lines
        ax_ce.plot(ce_cycles, ce_values, "r-", alpha=0.4)

        # Mark selected cycles in CE plot
        if Kcls:
            marked_ce_cycles = [c for c in Kcls if c in cycle_ce_values]
            marked_ce_values = [cycle_ce_values[c] for c in marked_ce_cycles]
            if marked_ce_cycles:
                ax_ce.scatter(
                    marked_ce_cycles,
                    marked_ce_values,
                    color="black",
                    marker="s",
                    s=50,
                    label=f"Selected CE {Kcls}",
                )

        # Set CE axis limits
        if ce_ylim:
            ax_ce.set_ylim(ce_ylim)
        else:
            ax_ce.set_ylim(0.98, 1.01)  # Default range

        # Add CE legend
        ax_ce.legend(loc="upper right")

    # Set capacity axis limits
    if capacity_ylim:
        ax_capacity.set_ylim(capacity_ylim)

    # Add capacity legend
    ax_capacity.legend(loc="upper left")

    # Plot 4: Capacity Change Rate (bottom-right) - 参考原代码
    ax_knee = axes[1, 1]
    ax_knee.set_title("Capacity Change Rate")
    ax_knee.set_xlabel("Cycle Number")
    ax_knee.set_ylabel("Change Rate (Ah/cycle)")
    ax_knee.grid(True, alpha=0.3)

    if cycle_capacities and len(cycle_capacities) > 3:
        cycles = sorted(cycle_capacities.keys())
        capacities = [cycle_capacities[c] for c in cycles]

        try:
            capacity_array = np.array(capacities)
            cycle_array = np.array(cycles)

            # Calculate capacity change rate using rolling window - 参考原代码
            window_size = min(5, len(cycles) - 1)
            if window_size >= 2:
                capacity_diff = np.diff(capacity_array)
                cycle_diff = np.diff(cycle_array)
                change_rate = capacity_diff / cycle_diff

                # Plot rate of change (smoothed)
                if len(change_rate) > window_size:
                    smoothed_rate = (
                        pd.Series(change_rate)
                        .rolling(window=window_size, center=True)
                        .mean()
                        .fillna(method="bfill")
                        .fillna(method="ffill")
                    )
                    ax_knee.plot(
                        cycles[1:],
                        smoothed_rate,
                        "b-",
                        linewidth=2,
                        label="Capacity Change Rate",
                    )
                else:
                    ax_knee.plot(
                        cycles[1:],
                        change_rate,
                        "b-",
                        linewidth=2,
                        label="Capacity Change Rate",
                    )

                # Plot reference line at zero
                ax_knee.axhline(y=0, color="r", linestyle="--", alpha=0.5)

                # Set Y-axis limits for change rate plot
                if change_rate_ylim:
                    ax_knee.set_ylim(change_rate_ylim)

                # Mark knee points if available
                if knee_points:
                    # Get current y-axis limits to place markers appropriately
                    y_min, y_max = ax_knee.get_ylim()
                    knee_y_values = [
                        y_min + 0.1 * (y_max - y_min) for _ in knee_points
                    ]  # Place markers at 10% from bottom
                    ax_knee.plot(
                        knee_points,
                        knee_y_values,
                        "rx",
                        markersize=10,
                        markeredgewidth=2,
                        label="Knee Points",
                    )

                # Mark selected cycles
                if Kcls:
                    marked_cycles = [
                        c for c in Kcls if c in cycles[1:]
                    ]  # Only cycles with change rate data
                    if marked_cycles:
                        # Get current y-axis limits to place markers appropriately
                        y_min, y_max = ax_knee.get_ylim()
                        marked_y_values = [
                            y_min + 0.05 * (y_max - y_min) for _ in marked_cycles
                        ]  # Place markers at 5% from bottom
                        ax_knee.plot(
                            marked_cycles,
                            marked_y_values,
                            "ko",
                            markersize=8,
                            markeredgewidth=2,
                            label=f"Selected Cycles {Kcls}",
                        )

                ax_knee.legend(loc="best")

        except Exception as e:
            log(f"Warning: Could not calculate capacity change rate: {str(e)}")

    # Adjust layout
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])

    # Save figure if path provided
    if save_path:
        plt.savefig(save_path, dpi=dpi, bbox_inches="tight")
        log(f"Figure saved to: {save_path}")

    # Show the plot
    plt.show()

    log("Battery analysis plots created successfully")
    return fig


def main():
    """Main function to run the battery analysis"""

    # 配置参数 - 用户可以修改这些参数
    JSON_FILE_PATH = r"E:\SOH+LP_rawdata_MIT_HUST_XJTU_TJU\XJTU_rawdata_mat\3C_battery-12_CV_Qcha_CE.json"  # 修改为您的JSON文件路径
    SAMPLE_NAME = "3C_battery-12_CV_Qcha_CE"  # 修改为您的样品名称
    KCLS = [1, 10, 50, 100]  # 要标记的循环数列表，可以修改为任意循环数
    SAVE_FIGURE = True  # 是否保存图片
    OUTPUT_PATH = f"{SAMPLE_NAME}_analysis_3.png"  # 输出图片路径

    # 自适应下采样配置 - 可以设置为整数（传统模式）或字典（自适应模式）
    # 传统模式: DOWNSAMPLE_PARAMS = 5
    # 自适应模式: 基于电流阈值的不同下采样因子
    DOWNSAMPLE_PARAMS = {
        "i_threshold": 1,  # 电流阈值 (A)，绝对值
        "factor_high": 5,  # 大电流区域下采样因子 (|current| > threshold)
        "factor_low": 2,  # 小电流区域下采样因子 (|current| <= threshold)
    }
    # 或者使用传统模式: DOWNSAMPLE_PARAMS = 5

    # 渐变色配置 - 可选择不同的颜色映射
    COLORMAP = (
        "tab10"  # 可选: 'viridis', 'plasma', 'coolwarm', 'jet', 'rainbow', 'tab10'
    )

    # Y轴范围设置 - 可以设置为None使用自动范围，或设置为(min, max)元组
    CURRENT_YLIM = (0, 2)  # 电流图Y轴范围，例如: (-2.0, 0.5)
    DIDT_YLIM = (-0.006, 0.001)  # dI/dt图Y轴范围，例如: (-0.1, 0.01)
    CAPACITY_YLIM = None  # 容量图左Y轴范围，例如: (0.8, 1.2)
    CE_YLIM = (0.98, 1.01)  # CE图右Y轴范围，例如: (0.98, 1.01)
    CHANGE_RATE_YLIM = None  # 容量变化率图Y轴范围，例如: (-0.05, 0.01)

    # 检查文件是否存在
    if not os.path.exists(JSON_FILE_PATH):
        log(f"Error: JSON file not found: {JSON_FILE_PATH}")
        log("Please modify the JSON_FILE_PATH variable in the main() function")
        return

    try:
        # Step 1: Extract data from JSON file
        log("Starting battery data analysis...")
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(
            JSON_FILE_PATH
        )

        if all_cycles_data is None:
            log("Error: Failed to extract data from JSON file")
            return

        # Step 2: Calculate dI/dt for all cycles
        didt_cycles_data = process_dIdt_data(
            all_cycles_data, downsample_params=DOWNSAMPLE_PARAMS
        )

        # Step 3: Detect knee points
        knee_points = detect_knee_points(cycle_capacities)
        if knee_points:
            log(f"Detected knee points at cycles: {knee_points}")
        else:
            log("No knee points detected")

        # Step 4: Create and display plots
        save_path = OUTPUT_PATH if SAVE_FIGURE else None
        fig = create_battery_analysis_plots(
            all_cycles_data=all_cycles_data,
            didt_cycles_data=didt_cycles_data,
            cycle_capacities=cycle_capacities,
            cycle_ce_values=cycle_ce_values,
            knee_points=knee_points,
            Kcls=KCLS,
            sample_name=SAMPLE_NAME,
            figsize=(15, 12),
            dpi=100,
            save_path=save_path,
            current_ylim=CURRENT_YLIM,
            didt_ylim=DIDT_YLIM,
            capacity_ylim=CAPACITY_YLIM,
            ce_ylim=CE_YLIM,
            change_rate_ylim=CHANGE_RATE_YLIM,
            colormap=COLORMAP,
        )

        log("Analysis completed successfully!")

        # Print summary
        log(f"Summary:")
        log(f"  - Total cycles processed: {len(all_cycles_data)}")
        log(f"  - Cycles with dI/dt data: {len(didt_cycles_data)}")
        log(f"  - Cycles with capacity data: {len(cycle_capacities)}")
        log(
            f"  - Cycles with CE data: {len(cycle_ce_values) if cycle_ce_values else 0}"
        )
        log(f"  - Auto-detected knee points: {knee_points}")
        log(f"  - Marked cycles: {KCLS}")
        log(f"  - Colormap used: {COLORMAP}")

        # Log downsampling configuration
        if isinstance(DOWNSAMPLE_PARAMS, dict):
            log(f"  - Adaptive downsampling:")
            log(f"    * Current threshold: {DOWNSAMPLE_PARAMS['i_threshold']} A")
            log(f"    * High current factor: {DOWNSAMPLE_PARAMS['factor_high']}")
            log(f"    * Low current factor: {DOWNSAMPLE_PARAMS['factor_low']}")
        else:
            log(f"  - Uniform downsampling factor: {DOWNSAMPLE_PARAMS}")

        # Log Y-axis ranges
        if CURRENT_YLIM:
            log(f"  - Current Y-axis range: {CURRENT_YLIM}")
        if DIDT_YLIM:
            log(f"  - dI/dt Y-axis range: {DIDT_YLIM}")
        if CAPACITY_YLIM:
            log(f"  - Capacity Y-axis range: {CAPACITY_YLIM}")
        if CE_YLIM:
            log(f"  - CE Y-axis range: {CE_YLIM}")
        if CHANGE_RATE_YLIM:
            log(f"  - Change rate Y-axis range: {CHANGE_RATE_YLIM}")

    except Exception as e:
        log(f"Error during analysis: {str(e)}")
        import traceback

        log(traceback.format_exc())


if __name__ == "__main__":
    main()
