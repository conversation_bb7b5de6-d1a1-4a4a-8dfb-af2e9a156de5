# 可视化模块使用指南

## 概述

`visualization.py` 模块提供了全面的数据可视化和分析工具，支持从原始数据探索到模型解释的完整可视化流程。该模块是系统可解释性和用户交互的重要支撑。

## 核心设计理念

### 多层次可视化
- **数据层**：原始电池数据的多维展示
- **算法层**：CV段检测、物理指标计算过程可视化
- **模型层**：注意力权重、特征重要性等模型内部机制展示
- **结果层**：检测结果、趋势分析、对比报告的图形化呈现

### 交互式分析
- **实时监控**：动态更新的在线检测界面
- **参数探索**：交互式参数调节和效果观察
- **深度分析**：点击钻取的多层次信息展示

## 核心类介绍

### 1. DataVisualizer 类

数据可视化器，专注于原始数据和处理过程的可视化。

#### 设计目标
为用户提供直观的数据理解工具，支持数据质量检查、异常发现和模式识别。

#### 关键函数说明

```python
def plot_battery_curves(t: np.ndarray, I: np.ndarray, V: np.ndarray, Q: np.ndarray, title: str = "Battery Curves") -> plt.Figure
```
- **功能**：绘制电池基础曲线(I-t, V-t, Q-t)
- **输入**：时间、电流、电压、容量序列
- **输出**：包含三子图的matplotlib图形
- **特色功能**：
  - 自动识别充放电阶段并用不同颜色标记
  - 显示关键时间点(CC/CV转换点)
  - 提供缩放和平移交互功能

```python
def plot_cv_segments(t: np.ndarray, I: np.ndarray, cv_segments: List[Tuple[int, int]], title: str = "CV Segments Detection") -> plt.Figure
```
- **功能**：可视化CV段检测结果
- **输入**：时间、电流序列、CV段边界列表
- **输出**：CV段检测可视化图形
- **可视化内容**：
  - 原始电流曲线
  - 检测到的CV段高亮显示
  - 段边界标记和持续时间标注
  - 检测置信度颜色编码

```python
def plot_physics_analysis(t: np.ndarray, I: np.ndarray, physics_data: Dict, title: str = "Physics Analysis") -> plt.Figure
```
- **功能**：绘制物理分析的多面板图
- **输入**：时间、电流序列、物理数据字典
- **输出**：包含多个子图的综合分析图
- **子图内容**：
  - 电流与其一阶、二阶导数
  - 平坦度分数时序图
  - 曲率分析图
  - 物理软指标条形图

```python
def plot_baseline_comparison(t: np.ndarray, I_original: np.ndarray, I_baseline: np.ndarray, I_delta: np.ndarray, title: str = "Baseline Comparison") -> plt.Figure
```
- **功能**：绘制基线对比分析图
- **输入**：时间、原始电流、基线电流、电流偏差
- **输出**：基线比较可视化图形
- **关键信息**：
  - 原始电流与基线电流对比
  - 正偏差(回嵌信号)高亮显示
  - 小电流权重覆盖显示
  - 回嵌强度积分区域填充

### 2. ModelVisualizer 类

模型可视化器，专注于深度学习模型的内部机制展示。

#### 设计目标
提供模型可解释性工具，帮助理解模型决策过程和物理先验的作用机制。

#### 关键函数说明

```python
def plot_attention_heatmap(attention_weights: np.ndarray, time_labels: Optional[List] = None, title: str = "Attention Heatmap") -> plt.Figure
```
- **功能**：绘制注意力权重热力图
- **输入**：注意力权重矩阵、时间标签(可选)
- **输出**：注意力热力图
- **可视化特点**：
  - 使用颜色深浅表示注意力强度
  - 对角线增强显示自注意力
  - 物理显著时间段的注意力模式突出
  - 支持多头注意力的分别显示

```python
def plot_physics_bias_visualization(physics_bias: np.ndarray, physics_signals: Dict, title: str = "Physics Bias Visualization") -> plt.Figure
```
- **功能**：可视化物理偏置矩阵及其构成
- **输入**：物理偏置矩阵、物理信号字典
- **输出**：物理偏置分析图
- **展示内容**：
  - 偏置矩阵热力图
  - 平坦度和曲率相似性贡献分解
  - 物理先验对注意力的影响分析

```python
def plot_feature_importance(feature_names: List[str], importance_scores: np.ndarray, title: str = "Feature Importance") -> plt.Figure
```
- **功能**：绘制特征重要性分析图
- **输入**：特征名称列表、重要性评分
- **输出**：特征重要性条形图
- **分析维度**：
  - 全局特征重要性排序
  - 物理特征vs数据驱动特征对比
  - 不同风险等级下的特征重要性差异

### 3. InteractiveVisualizer 类

交互式可视化器，提供动态交互和实时更新功能。

#### 设计目标
创建用户友好的交互界面，支持参数调节、实时监控和深度探索。

#### 关键函数说明

```python
def create_interactive_dashboard(data: Dict, title: str = "Lithium Plating Detection Dashboard") -> go.Figure
```
- **功能**：创建综合的交互式仪表板
- **输入**：数据字典、仪表板标题
- **输出**：plotly交互式仪表板
- **仪表板组件**：
  - 实时风险评分表盘
  - 历史趋势折线图
  - 物理指标雷达图
  - 检测结果分布饼图
  - 告警状态指示器

```python
def create_realtime_monitor(stream_data: Dict, update_interval: int = 1000) -> go.Figure
```
- **功能**：创建实时监控界面
- **输入**：流数据字典、更新间隔(毫秒)
- **输出**：实时更新的监控界面
- **监控内容**：
  - 滑动窗口的实时数据曲线
  - 当前风险评分和等级
  - 最近检测历史
  - 系统状态监控

```python
def create_parameter_explorer(data: Dict, parameter_ranges: Dict) -> go.Figure
```
- **功能**：创建参数探索界面
- **输入**：数据字典、参数范围字典
- **输出**：参数调节交互界面
- **功能特点**：
  - 滑块控制参数调节
  - 实时更新检测结果
  - 参数敏感性分析
  - 最优参数建议

### 4. StatisticalVisualizer 类

统计分析可视化器，提供深度的统计分析图表。

#### 设计目标
通过统计可视化揭示数据模式、验证算法性能和支持科学决策。

#### 关键函数说明

```python
def plot_distribution_analysis(data: np.ndarray, bins: int = 50, title: str = "Distribution Analysis") -> plt.Figure
```
- **功能**：绘制数据分布分析图
- **输入**：数据数组、直方图箱数
- **输出**：分布分析组合图
- **分析内容**：
  - 直方图和核密度估计
  - 正态性检验结果
  - 分位数标记
  - 异常值识别

```python
def plot_roc_curves(true_labels: np.ndarray, prediction_scores: np.ndarray, model_names: List[str], title: str = "ROC Curves") -> plt.Figure
```
- **功能**：绘制ROC曲线比较图
- **输入**：真实标签、预测分数、模型名称列表
- **输出**：多模型ROC曲线比较图
- **比较维度**：
  - 不同模型的ROC曲线
  - AUC值标注和排序
  - 最佳工作点标记
  - 置信区间显示

### 5. ExplanationVisualizer 类

解释性可视化器，专注于模型决策的可解释性展示。

#### 设计目标
将抽象的模型决策过程转化为直观的视觉解释，增强用户对系统的信任和理解。

#### 关键函数说明

```python
def plot_physics_explanation(physics_metrics: Dict, risk_contributions: Dict, title: str = "Physics-based Explanation") -> plt.Figure
```
- **功能**：绘制基于物理的解释图
- **输入**：物理指标字典、风险贡献度字典
- **输出**：物理解释综合图
- **解释维度**：
  - 各物理指标的数值和正常范围对比
  - 每个指标对最终风险评分的贡献度
  - 指标间的相互关系网络图
  - 关键物理现象的时间定位

```python
def plot_temporal_explanation(time_series: np.ndarray, importance_weights: np.ndarray, physics_events: Dict, title: str = "Temporal Explanation") -> plt.Figure
```
- **功能**：绘制时间序列解释图
- **输入**：时间序列数据、重要性权重、物理事件字典
- **输出**：时序解释可视化图
- **解释内容**：
  - 时间序列数据与重要性权重叠加
  - 关键时间点的物理事件标注
  - 决策影响的时间范围标记
  - 模型注意力的时间分布

## 使用示例

### 基础数据可视化
```python
from visualization import DataVisualizer

# 初始化可视化器
visualizer = DataVisualizer(style_config={
    'theme': 'seaborn',
    'color_palette': 'viridis',
    'figure_size': (12, 8)
})

# 绘制电池基础曲线
fig = visualizer.plot_battery_curves(t, I, V, Q, title="电池循环1250数据")
fig.savefig('battery_curves.png', dpi=300, bbox_inches='tight')

# 可视化CV段检测
cv_segments = [(1200, 1450), (2100, 2350)]  # 检测到的CV段
fig_cv = visualizer.plot_cv_segments(t, I, cv_segments, title="CV段检测结果")

# 物理分析可视化
physics_data = {
    'derivatives': (dI_dt, d2I_dt2),
    'flatness_scores': flatness_scores,
    'curvature': curvature_values,
    'metrics': {'S_plat': 0.78, 'S_bump': 0.62, 'S_amp': 0.012}
}
fig_physics = visualizer.plot_physics_analysis(t, I, physics_data)
```

### 模型解释可视化
```python
from visualization import ModelVisualizer, ExplanationVisualizer

model_viz = ModelVisualizer()
exp_viz = ExplanationVisualizer()

# 注意力权重可视化
attention_weights = model_output['attention_weights']  # [seq_len, seq_len]
time_labels = [f"{i*0.1:.1f}s" for i in range(len(t))]
fig_att = model_viz.plot_attention_heatmap(
    attention_weights, time_labels, "物理增强注意力权重"
)

# 物理解释可视化
physics_metrics = {
    'S_plat': 0.78, 'S_bump': 0.62, 'S_mono': 0.03,
    'S_amp': 0.012, 'Q_tilde': 0.004, 'C_low': 0.80, 'C_kappa': 0.65
}
risk_contributions = {
    'S_plat': 0.25, 'S_bump': 0.30, 'S_amp': 0.35, 'others': 0.10
}
fig_exp = exp_viz.plot_physics_explanation(
    physics_metrics, risk_contributions, "风险评分物理解释"
)

# 时序解释
importance_weights = model_output['temporal_importance']
physics_events = {
    1200: {'event': 'CV开始', 'type': 'transition'},
    1350: {'event': '电流平台', 'type': 'anomaly'},
    1420: {'event': '回嵌峰值', 'type': 'critical'}
}
fig_temporal = exp_viz.plot_temporal_explanation(
    I, importance_weights, physics_events, "时序决策解释"
)
```

### 交互式仪表板
```python
from visualization import InteractiveVisualizer

interactive_viz = InteractiveVisualizer()

# 创建综合仪表板
dashboard_data = {
    'current_risk': 0.85,
    'risk_history': risk_scores_24h,
    'physics_metrics': physics_metrics,
    'detection_counts': {'normal': 145, 'warning': 23, 'critical': 5},
    'system_status': 'online'
}

dashboard = interactive_viz.create_interactive_dashboard(
    dashboard_data, "析锂检测实时监控仪表板"
)

# 启动本地服务器显示
dashboard.show()

# 或保存为HTML文件
dashboard.write_html("dashboard.html")
```

### 实时监控界面
```python
# 实时数据流配置
stream_data = {
    'buffer_size': 1000,  # 显示最近1000个数据点
    'data_source': 'kafka://localhost:9092/battery_data',
    'update_frequency': 1.0  # 每秒更新
}

# 创建实时监控
monitor = interactive_viz.create_realtime_monitor(
    stream_data, update_interval=1000
)

# 启动实时更新
monitor.show()
```

### 参数敏感性分析
```python
# 参数探索配置
parameter_ranges = {
    'v_var_th': (1e-5, 1e-3, 1e-4),      # (min, max, default)
    'i_slope_th': (-1e-4, -1e-6, -1e-5),
    'tau_slope': (0.001, 0.005, 0.002),
    'alpha_base': (0.95, 0.995, 0.98)
}

# 创建参数探索界面
explorer = interactive_viz.create_parameter_explorer(
    test_data, parameter_ranges
)

# 添加回调函数，实时更新结果
@explorer.callback
def update_results(v_var_th, i_slope_th, tau_slope, alpha_base):
    # 使用新参数重新计算
    new_params = {
        'v_var_th': v_var_th,
        'i_slope_th': i_slope_th,
        'tau_slope': tau_slope,
        'alpha_base': alpha_base
    }
    
    # 重新检测和计算指标
    results = recompute_with_params(test_data, new_params)
    
    # 返回更新的图表数据
    return update_plots(results)
```

### 性能分析可视化
```python
from visualization import StatisticalVisualizer

stat_viz = StatisticalVisualizer()

# 模型性能比较
models = ['物理增强Transformer', '标准LSTM', '传统规则']
true_labels = test_labels
predictions = [model1_pred, model2_pred, model3_pred]

# ROC曲线比较
fig_roc = stat_viz.plot_roc_curves(
    true_labels, predictions, models, "模型性能ROC比较"
)

# PR曲线比较
fig_pr = stat_viz.plot_precision_recall_curves(
    true_labels, predictions, models, "精确率-召回率曲线"
)

# 混淆矩阵
fig_cm = stat_viz.plot_confusion_matrix(
    true_labels, best_predictions, ['正常', '析锂'], "混淆矩阵"
)

# 特征分布分析
risk_scores = [r.risk_score for r in detection_results]
fig_dist = stat_viz.plot_distribution_analysis(
    np.array(risk_scores), bins=30, "风险评分分布分析"
)
```

## 自定义样式设置

### 发表级图表样式
```python
from visualization import CustomPlotStyles

styles = CustomPlotStyles()

# 设置发表级样式
styles.set_publication_style()

# 自定义配置
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'Times New Roman',
    'figure.dpi': 300,
    'axes.linewidth': 1.5,
    'lines.linewidth': 2,
    'grid.alpha': 0.3
})

# 物理指标专用颜色映射
physics_colors = styles.get_physics_color_map()
# {'S_plat': '#1f77b4', 'S_bump': '#ff7f0e', 'S_amp': '#d62728', ...}
```

### 主题定制
```python
# 暗色主题(适合实时监控)
styles.set_dark_theme()

# 演示主题(适合汇报展示)
styles.set_presentation_style()

# 自定义调色板
custom_colors = styles.create_color_palette(
    n_colors=7, palette_name='physics_palette'
)
```

## 动画和交互功能

### 训练过程动画
```python
from visualization import AnimationCreator

animator = AnimationCreator()

# 创建训练损失动画
loss_history = training_logs['loss']
metric_history = training_logs['f1_score']

animator.create_training_animation(loss_history, metric_history)

# 保存为GIF
animator.save_animation(animation_obj, 'training_progress.gif', fps=10)
```

### 检测过程动画
```python
# 创建检测过程动画
sequence_data = battery_cycle_data
detection_steps = [
    {'step': 'CV检测', 'data': cv_detection_result},
    {'step': '物理计算', 'data': physics_metrics},
    {'step': '风险评估', 'data': risk_assessment}
]

animator.create_detection_process_animation(sequence_data, detection_steps)
```

## 报告生成

### 自动化报告生成
```python
from visualization import ReportGenerator

report_gen = ReportGenerator(template_path='templates/analysis_template.html')

# 生成综合分析报告
analysis_data = {
    'detection_results': results,
    'performance_metrics': metrics,
    'trend_analysis': trends,
    'recommendations': recommendations
}

report_path = report_gen.generate_analysis_report(
    analysis_data, 'monthly_analysis_report.html'
)

# 生成模型性能报告
model_data = {
    'architecture': model_config,
    'training_history': training_logs,
    'test_results': test_metrics
}

performance_data = {
    'accuracy': 0.94,
    'precision': 0.91,
    'recall': 0.89,
    'f1_score': 0.90
}

model_report = report_gen.generate_model_report(
    model_data, performance_data, 'model_performance_report.html'
)
```

### 执行摘要生成
```python
# 关键发现摘要
key_findings = {
    'high_risk_batteries': ['Cell_001', 'Cell_045', 'Cell_128'],
    'detection_accuracy': 0.94,
    'false_positive_rate': 0.06,
    'critical_physics_indicators': ['S_amp', 'S_bump'],
    'recommended_actions': [
        '降低Cell_001充电电流',
        '检查Cell_045温度控制',
        '更换Cell_128电池'
    ]
}

# 生成图表
charts = [fig_roc, fig_trend, fig_distribution]

# 创建执行摘要
summary_path = report_gen.create_executive_summary(key_findings, charts)
```

## 性能优化

### 大数据可视化优化
```python
# 数据降采样
def downsample_for_visualization(data, max_points=10000):
    """为可视化目的对大数据集进行智能降采样"""
    if len(data) <= max_points:
        return data
    
    # 保留关键点(极值、转折点)的降采样
    indices = np.linspace(0, len(data)-1, max_points, dtype=int)
    return data[indices]

# 渐进式渲染
def progressive_render(large_dataset):
    """大数据集的渐进式渲染"""
    # 首先显示低分辨率版本
    quick_view = downsample_for_visualization(large_dataset, 1000)
    quick_fig = plot_quick_overview(quick_view)
    
    # 后台计算高分辨率版本
    full_fig = plot_detailed_view(large_dataset)
    
    return quick_fig, full_fig
```

### 交互响应优化
```python
# 缓存计算结果
from functools import lru_cache

@lru_cache(maxsize=100)
def cached_physics_calculation(data_hash, params_hash):
    """缓存物理指标计算结果"""
    # 避免重复计算相同参数下的结果
    pass

# 异步更新
import asyncio

async def async_plot_update(data, params):
    """异步更新图表，避免界面阻塞"""
    # 在后台线程中进行计算密集型操作
    result = await asyncio.to_thread(compute_heavy_analysis, data, params)
    
    # 在主线程中更新界面
    update_plot(result)
```

## 故障诊断

### 可视化质量检查
```python
def check_visualization_quality(fig):
    """检查可视化图表的质量"""
    quality_report = {
        'has_title': bool(fig.get_suptitle()),
        'has_labels': all([ax.get_xlabel() and ax.get_ylabel() 
                          for ax in fig.get_axes()]),
        'has_legend': any([ax.get_legend() for ax in fig.get_axes()]),
        'readable_text': check_text_readability(fig),
        'color_accessibility': check_color_accessibility(fig)
    }
    
    return quality_report

def validate_data_for_plotting(data):
    """验证数据是否适合绘图"""
    issues = []
    
    if np.any(np.isnan(data)):
        issues.append("数据包含NaN值")
    
    if np.any(np.isinf(data)):
        issues.append("数据包含无穷值")
    
    if len(data) == 0:
        issues.append("数据为空")
    
    return issues
```

## 最佳实践

### 可视化设计原则
1. **清晰性优先**：确保图表信息传达清晰
2. **一致性保持**：统一的颜色、字体、样式
3. **交互友好**：提供必要的交互功能
4. **性能考虑**：大数据集的优化处理
5. **可访问性**：考虑色盲用户的需求

### 代码组织建议
```python
# 可视化配置集中管理
CONFIG = {
    'figure_size': (12, 8),
    'dpi': 300,
    'font_size': 12,
    'color_palette': 'physics_optimized',
    'style_theme': 'publication'
}

# 可视化函数标准模板
def plot_template(data, config=None, **kwargs):
    """标准可视化函数模板"""
    # 1. 参数验证和默认值设置
    config = config or CONFIG
    
    # 2. 数据验证和预处理
    issues = validate_data_for_plotting(data)
    if issues:
        raise ValueError(f"数据问题: {issues}")
    
    # 3. 创建图形和绘制
    fig, ax = plt.subplots(figsize=config['figure_size'])
    
    # 4. 样式设置
    apply_style(fig, ax, config)
    
    # 5. 返回图形对象
    return fig
```

该可视化模块通过丰富的图表类型和交互功能，为析锂检测系统提供了强大的数据洞察和模型解释能力，是实现系统可解释性和用户友好性的关键组件。
