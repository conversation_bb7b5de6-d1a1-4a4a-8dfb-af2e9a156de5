<!-- Generated by SpecStory -->

# 锂检测框架代码指导文档 (2025-08-04 08:44Z)

_**User**_

此处是描述一个锂检测框架，基于电池恒压阶段的I,T,Q——电流，时间，对应时间下的容量去做一个析锂的检出，给出一个代码的检出方法的描述，分为：
1、数据导入、提取、滤波，最终的数据获得
2、特征提取与析锂与否的判别量（即y值，其与当圈循环的库伦效率CE相关，具体CE低至阈值CE_pr认为是析锂由后续设定决定），后期可能增加其他判别量
3、我认定的特征为基于恒压过程下电流I、容量Q随时间的变化趋势认定，非析锂为平滑下降（析锂对应的特征为出现非平滑性下降，如平台、异常凸起然后下降这样数据）
4、基于这个特征以及每个电池数据集对应的判别量建立物理信息神经网络，实现通过特征估计是否发生析锂
在当前界面写一个指导性.md文件，以便后续代码与模型的建立

---

_**Assistant**_



---

