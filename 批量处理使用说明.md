# 批量电池数据分析工具使用说明

## 概述

我为您创建了三个批量处理脚本，可以自动处理指定路径下的所有JSON文件，并生成对应的分析图表。

## 脚本说明

### 1. `batch_battery_analysis.py` - 完整功能版本
**特点**: 功能最全面，支持自定义配置
**适用**: 需要灵活配置参数的场景

**使用方法**:
```bash
python batch_battery_analysis.py
```
- 交互式界面，可以自定义所有参数
- 支持自定义标记循环、颜色映射、DPI等
- 详细的处理日志和错误报告

### 2. `quick_batch_analysis.py` - 快速处理版本
**特点**: 使用您当前的配置，快速批量处理
**适用**: 日常批量处理，无需修改参数

**使用方法**:
```bash
# 命令行模式
python quick_batch_analysis.py "E:\your\path\to\json\files"

# 交互模式
python quick_batch_analysis.py
```

**当前配置**:
- 标记循环: [2, 20, 50, 150]
- 颜色映射: Blues
- DPI: 330
- 电流阈值: 1A (高电流因子:5, 低电流因子:3)
- Y轴范围: 电流(0,2), dI/dt(-0.006,0.001), CE(0.98,1.01)

### 3. `drag_drop_batch_analysis.py` - 拖拽式版本
**特点**: 最简单易用，支持拖拽操作
**适用**: 快速处理，无需命令行操作

**使用方法**:
1. **拖拽方式**: 直接将文件夹或JSON文件拖拽到脚本文件上
2. **双击运行**: 双击脚本，然后输入路径

## 功能特性

### 自动样品名称提取
- 从文件名自动提取样品名称
- 自动移除常见后缀: `_CV_Qcha_CE`, `_data`, `_battery`, `_cycle`
- 例如: `3C_battery-12_CV_Qcha_CE.json` → `3C_battery-12`

### 智能路径处理
- **单文件**: 处理指定的JSON文件
- **目录**: 处理目录下所有JSON文件
- **输出**: 图片保存在与输入文件相同的目录

### 批量处理统计
- 实时显示处理进度
- 统计成功/失败数量
- 显示成功率和详细错误信息

## 输出文件命名规则

```
输入文件: 3C_battery-12_CV_Qcha_CE.json
输出文件: 3C_battery-12_analysis.png
```

## 使用示例

### 示例1: 处理单个文件
```bash
python quick_batch_analysis.py "E:\data\3C_battery-12_CV_Qcha_CE.json"
```

### 示例2: 处理整个目录
```bash
python quick_batch_analysis.py "E:\SOH+LP_rawdata_MIT_HUST_XJTU_TJU\XJTU_rawdata_mat"
```

### 示例3: 拖拽处理
1. 打开文件管理器
2. 找到包含JSON文件的文件夹
3. 将文件夹拖拽到 `drag_drop_batch_analysis.py` 文件上
4. 自动开始处理

## 处理流程

每个JSON文件的处理流程：
1. **提取样品名称** - 从文件名自动提取
2. **数据提取** - 读取循环数据、容量、CE值
3. **dI/dt计算** - 使用自适应下采样
4. **膝点检测** - 自动检测容量衰减转折点
5. **图表生成** - 创建四象限分析图
6. **保存图片** - 高分辨率PNG格式

## 输出图表内容

每个分析图包含四个子图：
1. **电流vs时间** (左上) - Blues渐变色，标记指定循环
2. **dI/dt vs时间** (右上) - 电流变化率分析
3. **容量vs循环** (左下) - 左轴容量，右轴CE值
4. **容量变化率** (右下) - 容量衰减速率分析

## 错误处理

### 常见错误及解决方案

1. **文件未找到**
   - 检查路径是否正确
   - 确保文件存在且可读

2. **数据格式错误**
   - 检查JSON文件格式
   - 确保包含必要字段

3. **内存不足**
   - 减少同时处理的文件数量
   - 增加下采样因子

4. **权限错误**
   - 确保对输出目录有写入权限
   - 以管理员身份运行

## 性能优化建议

### 大批量处理优化
- 使用 `quick_batch_analysis.py` 获得最佳性能
- 适当增加下采样因子减少内存占用
- 分批处理大量文件

### 自定义配置
如需修改默认配置，编辑脚本中的CONFIG部分：

```python
CONFIG = {
    'kcls': [2, 20, 50, 150],  # 修改标记循环
    'colormap': 'Blues',       # 修改颜色映射
    'dpi': 330,                # 修改图片分辨率
    # ... 其他参数
}
```

## 技术支持

如遇到问题：
1. 检查Python环境和依赖库
2. 查看控制台错误信息
3. 确认JSON文件格式正确
4. 验证文件路径和权限

## 更新日志

- v1.0: 基础批量处理功能
- v1.1: 添加自适应下采样和可调渐变色
- v1.2: 添加拖拽式界面和快速处理模式
