# 基础计算和数据处理
numpy>=1.19.0
pandas>=1.3.0
scipy>=1.7.0

# 深度学习框架
torch>=1.9.0
torchvision>=0.10.0
transformers>=4.10.0

# 机器学习工具
scikit-learn>=1.0.0
xgboost>=1.5.0

# 数据存储和I/O
h5py>=3.1.0
openpyxl>=3.0.9
xlsxwriter>=3.0.0

# 可视化工具
matplotlib>=3.3.0
seaborn>=0.11.0
plotly>=5.0.0
bokeh>=2.4.0

# 信号处理
scipy>=1.7.0
PyWavelets>=1.1.0

# 数学计算优化
numba>=0.54.0
cython>=0.29.0

# Web框架和API
flask>=2.0.0
fastapi>=0.70.0
uvicorn>=0.15.0
streamlit>=1.0.0

# 数据库连接
sqlalchemy>=1.4.0
pymongo>=3.12.0
redis>=3.5.0

# 配置管理
pyyaml>=5.4.0
configparser>=5.0.0
python-dotenv>=0.19.0

# 日志和监控
loguru>=0.5.0
prometheus-client>=0.11.0
sentry-sdk>=1.4.0

# 测试工具
pytest>=6.2.0
pytest-cov>=2.12.0
pytest-html>=3.1.0
pytest-mock>=3.6.0

# 代码质量
black>=21.0.0
flake8>=3.9.0
mypy>=0.910
isort>=5.9.0

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0
mkdocs>=1.2.0

# 数据科学工具
jupyter>=1.0.0
ipywidgets>=7.6.0
tqdm>=4.62.0

# 性能分析
memory-profiler>=0.58.0
line-profiler>=3.3.0
py-spy>=0.3.0

# 模型优化
onnx>=1.10.0
onnxruntime>=1.8.0
tensorboard>=2.7.0

# 时间序列分析
statsmodels>=0.12.0
pykalman>=0.9.5

# 图像处理(用于可视化)
pillow>=8.3.0
opencv-python>=4.5.0

# 网络和通信
requests>=2.25.0
websockets>=9.1.0
paho-mqtt>=1.5.0

# 加密和安全
cryptography>=3.4.0
bcrypt>=3.2.0

# 任务调度
celery>=5.1.0
schedule>=1.1.0

# 实用工具
click>=8.0.0
rich>=10.0.0
tabulate>=0.8.0
progressbar2>=3.53.0
