
"""
电池数据分析工具使用示例
Battery Analysis Tool Usage Example

这个示例展示了如何使用修改后的电池分析工具
This example shows how to use the modified battery analysis tool
"""

from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)
import os

def run_battery_analysis():
    """运行电池数据分析的完整流程"""
    
    # =============================================================================
    # 配置参数 - 请根据您的需求修改这些参数
    # Configuration Parameters - Please modify these according to your needs
    # =============================================================================
    
    # 1. 输入文件路径 (Input file path)
    JSON_FILE_PATH = "your_battery_data.json"  # 请修改为您的JSON文件路径
    
    # 2. 样品名称 (Sample name)
    SAMPLE_NAME = "Battery_Sample_Test"  # 修改为您的样品名称
    
    # 3. 要标记的循环数 (Cycles to mark)
    # 这个列表中的循环数将在所有图表中用黑色标记
    # Cycles in this list will be marked in black on all plots
    KCLS = [1, 5, 10, 20, 50]  # 可以修改为任意循环数列表
    
    # 4. 处理参数 (Processing parameters)
    DOWNSAMPLE_FACTOR = 1  # 下采样因子，1表示不下采样 (1 means no downsampling)
    
    # 5. 输出设置 (Output settings)
    SAVE_FIGURE = True  # 是否保存图片 (Whether to save figure)
    OUTPUT_PATH = f"{SAMPLE_NAME}_analysis.png"  # 输出图片路径
    FIGURE_SIZE = (15, 12)  # 图片尺寸 (Figure size)
    DPI = 150  # 图片分辨率 (Figure DPI)
    
    # =============================================================================
    # 开始分析流程
    # Start Analysis Process
    # =============================================================================
    
    # 检查文件是否存在
    if not os.path.exists(JSON_FILE_PATH):
        log(f"错误: 找不到JSON文件: {JSON_FILE_PATH}")
        log("请修改 JSON_FILE_PATH 变量为正确的文件路径")
        log(f"Error: JSON file not found: {JSON_FILE_PATH}")
        log("Please modify the JSON_FILE_PATH variable to the correct file path")
        return False
    
    try:
        log("="*60)
        log("开始电池数据分析 / Starting battery data analysis...")
        log("="*60)
        
        # 步骤1: 从JSON文件提取数据
        log("步骤1: 提取数据 / Step 1: Extracting data...")
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(JSON_FILE_PATH)
        
        if all_cycles_data is None:
            log("错误: 无法从JSON文件提取数据 / Error: Failed to extract data from JSON file")
            return False
        
        # 步骤2: 计算所有循环的dI/dt
        log("步骤2: 计算dI/dt / Step 2: Calculating dI/dt...")
        didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_factor=DOWNSAMPLE_FACTOR)
        
        # 步骤3: 检测膝点
        log("步骤3: 检测膝点 / Step 3: Detecting knee points...")
        knee_points = detect_knee_points(cycle_capacities)
        if knee_points:
            log(f"检测到膝点位于循环: {knee_points} / Detected knee points at cycles: {knee_points}")
        else:
            log("未检测到膝点 / No knee points detected")
        
        # 步骤4: 创建和显示图表
        log("步骤4: 创建图表 / Step 4: Creating plots...")
        save_path = OUTPUT_PATH if SAVE_FIGURE else None
        
        fig = create_battery_analysis_plots(
            all_cycles_data=all_cycles_data,
            didt_cycles_data=didt_cycles_data,
            cycle_capacities=cycle_capacities,
            knee_points=knee_points,
            Kcls=KCLS,
            sample_name=SAMPLE_NAME,
            figsize=FIGURE_SIZE,
            dpi=DPI,
            save_path=save_path
        )
        
        # 输出分析总结
        log("="*60)
        log("分析完成! / Analysis completed successfully!")
        log("="*60)
        log("分析总结 / Analysis Summary:")
        log(f"  - 处理的总循环数 / Total cycles processed: {len(all_cycles_data)}")
        log(f"  - 有dI/dt数据的循环数 / Cycles with dI/dt data: {len(didt_cycles_data)}")
        log(f"  - 有容量数据的循环数 / Cycles with capacity data: {len(cycle_capacities)}")
        log(f"  - 自动检测的膝点 / Auto-detected knee points: {knee_points}")
        log(f"  - 标记的循环 / Marked cycles: {KCLS}")
        if SAVE_FIGURE:
            log(f"  - 图片已保存至 / Figure saved to: {OUTPUT_PATH}")
        log("="*60)
        
        return True
        
    except Exception as e:
        log(f"分析过程中出现错误 / Error during analysis: {str(e)}")
        import traceback
        log(traceback.format_exc())
        return False


def create_sample_json_data():
    """创建示例JSON数据文件用于测试"""
    import json
    import numpy as np
    
    log("创建示例数据文件...")
    
    # 生成示例数据
    sample_data = {}
    
    # 生成10个循环的示例数据
    for cycle in range(1, 11):
        # 生成时间数据 (分钟)
        time_points = 100
        time_min = np.linspace(0, 2, time_points)  # 2分钟的测试时间
        
        # 生成电流数据 (模拟放电曲线)
        current_A = []
        for t in time_min:
            if t < 0.1:  # 初始阶段
                current = -1.0
            elif t < 1.5:  # 主要放电阶段
                current = -1.0 + 0.1 * np.sin(t * 10) * np.exp(-t/2)
            else:  # 结束阶段
                current = -0.1 * (2 - t)
            
            # 添加一些噪声
            current += np.random.normal(0, 0.01)
            current_A.append(current)
        
        # 存储循环数据
        sample_data[f"Cycle_{cycle}"] = {
            "relative_time_min": time_min.tolist(),
            "current_A": current_A
        }
    
    # 生成容量数据 (模拟容量衰减)
    capacities = []
    for cycle in range(1, 11):
        capacity = 1.0 - 0.02 * (cycle - 1) - 0.001 * (cycle - 1)**2  # 模拟衰减
        capacities.append(capacity)
    
    sample_data["Discharge Capacity"] = capacities
    
    # 生成库伦效率数据
    ce_values = []
    for cycle in range(1, 11):
        ce = 0.98 + 0.01 * np.random.random()  # 模拟CE值
        ce_values.append(ce)
    
    sample_data["CE"] = ce_values
    
    # 保存到文件
    filename = "sample_battery_data.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, indent=2)
    
    log(f"示例数据文件已创建: {filename}")
    return filename


def main():
    """主函数"""
    print("电池数据分析工具 / Battery Data Analysis Tool")
    print("="*60)
    
    # 询问用户是否需要创建示例数据
    create_sample = input("是否创建示例数据文件进行测试? (y/n) / Create sample data file for testing? (y/n): ").lower().strip()
    
    if create_sample in ['y', 'yes', '是']:
        sample_file = create_sample_json_data()
        print(f"\n示例数据文件已创建: {sample_file}")
        print("请修改 run_battery_analysis() 函数中的 JSON_FILE_PATH 变量")
        print(f"Sample data file created: {sample_file}")
        print("Please modify the JSON_FILE_PATH variable in run_battery_analysis() function")
        
        # 自动使用示例文件运行分析
        use_sample = input("\n是否使用示例文件运行分析? (y/n) / Run analysis with sample file? (y/n): ").lower().strip()
        if use_sample in ['y', 'yes', '是']:
            # 临时修改文件路径
            import battery_analysis_direct_plot
            original_main = battery_analysis_direct_plot.main
            
            def temp_main():
                JSON_FILE_PATH = sample_file
                SAMPLE_NAME = "Sample_Battery_Test"
                KCLS = [1, 3, 5, 8, 10]
                DOWNSAMPLE_FACTOR = 1
                SAVE_FIGURE = True
                OUTPUT_PATH = f"{SAMPLE_NAME}_analysis.png"
                
                try:
                    all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(JSON_FILE_PATH)
                    if all_cycles_data is None:
                        log("Error: Failed to extract data from JSON file")
                        return
                    
                    didt_cycles_data = process_dIdt_data(all_cycles_data, downsample_factor=DOWNSAMPLE_FACTOR)
                    knee_points = detect_knee_points(cycle_capacities)
                    
                    if knee_points:
                        log(f"Detected knee points at cycles: {knee_points}")
                    else:
                        log("No knee points detected")
                    
                    save_path = OUTPUT_PATH if SAVE_FIGURE else None
                    create_battery_analysis_plots(
                        all_cycles_data=all_cycles_data,
                        didt_cycles_data=didt_cycles_data,
                        cycle_capacities=cycle_capacities,
                        knee_points=knee_points,
                        Kcls=KCLS,
                        sample_name=SAMPLE_NAME,
                        figsize=(15, 12),
                        dpi=100,
                        save_path=save_path
                    )
                    
                    log("Analysis completed successfully!")
                    log(f"Summary:")
                    log(f"  - Total cycles processed: {len(all_cycles_data)}")
                    log(f"  - Cycles with dI/dt data: {len(didt_cycles_data)}")
                    log(f"  - Cycles with capacity data: {len(cycle_capacities)}")
                    log(f"  - Auto-detected knee points: {knee_points}")
                    log(f"  - Marked cycles: {KCLS}")
                    
                except Exception as e:
                    log(f"Error during analysis: {str(e)}")
                    import traceback
                    log(traceback.format_exc())
            
            temp_main()
    else:
        print("\n请按照以下步骤使用工具:")
        print("Please follow these steps to use the tool:")
        print("1. 准备您的JSON数据文件 / Prepare your JSON data file")
        print("2. 修改 run_battery_analysis() 函数中的参数 / Modify parameters in run_battery_analysis() function")
        print("3. 运行 run_battery_analysis() / Run run_battery_analysis()")
        
        # 运行用户的分析
        run_analysis = input("\n是否现在运行分析? (y/n) / Run analysis now? (y/n): ").lower().strip()
        if run_analysis in ['y', 'yes', '是']:
            run_battery_analysis()


if __name__ == "__main__":
    main()

