"""
Y轴范围功能使用示例
Y-axis Range Functionality Usage Example

这个示例展示了如何使用新增的Y轴范围自定义功能
This example demonstrates how to use the new Y-axis range customization features
"""

from battery_analysis_direct_plot import (
    extract_cycles_from_json,
    process_dIdt_data,
    detect_knee_points,
    create_battery_analysis_plots,
    log
)

def example_with_custom_ylim():
    """使用自定义Y轴范围的示例"""
    
    # =============================================================================
    # 配置参数示例 - Y轴范围自定义
    # Configuration Example - Y-axis Range Customization
    # =============================================================================
    
    # 基本配置
    JSON_FILE_PATH = "your_battery_data.json"  # 替换为您的数据文件
    SAMPLE_NAME = "Custom_YLim_Example"
    KCLS = [1, 10, 25, 50]  # 要标记的循环
    
    # Y轴范围自定义示例
    # 设置为None表示使用自动范围，设置为(min, max)表示使用自定义范围
    
    # 示例1: 所有图表使用自定义范围
    CURRENT_YLIM = (-2.0, 0.5)      # 电流图: -2.0A 到 0.5A
    DIDT_YLIM = (-0.1, 0.01)        # dI/dt图: -0.1 到 0.01 A/s
    CAPACITY_YLIM = (0.8, 1.2)      # 容量图: 0.8 到 1.2 Ah
    CE_YLIM = (0.98, 1.01)          # CE图: 0.98 到 1.01
    CHANGE_RATE_YLIM = (-0.05, 0.01) # 变化率图: -0.05 到 0.01 Ah/cycle
    
    try:
        log("="*60)
        log("Y轴范围自定义示例 / Y-axis Range Customization Example")
        log("="*60)
        
        # 数据处理步骤
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(JSON_FILE_PATH)
        if all_cycles_data is None:
            log("错误: 无法提取数据 / Error: Failed to extract data")
            return
        
        didt_cycles_data = process_dIdt_data(all_cycles_data)
        knee_points = detect_knee_points(cycle_capacities)
        
        # 创建图表 - 使用自定义Y轴范围
        fig = create_battery_analysis_plots(
            all_cycles_data=all_cycles_data,
            didt_cycles_data=didt_cycles_data,
            cycle_capacities=cycle_capacities,
            cycle_ce_values=cycle_ce_values,
            knee_points=knee_points,
            Kcls=KCLS,
            sample_name=SAMPLE_NAME,
            figsize=(15, 12),
            dpi=100,
            save_path=f"{SAMPLE_NAME}_custom_ylim.png",
            # 新增的Y轴范围参数
            current_ylim=CURRENT_YLIM,
            didt_ylim=DIDT_YLIM,
            capacity_ylim=CAPACITY_YLIM,
            ce_ylim=CE_YLIM,
            change_rate_ylim=CHANGE_RATE_YLIM,
        )
        
        log("✓ 自定义Y轴范围图表创建成功!")
        log(f"应用的Y轴范围:")
        log(f"  - 电流图: {CURRENT_YLIM}")
        log(f"  - dI/dt图: {DIDT_YLIM}")
        log(f"  - 容量图: {CAPACITY_YLIM}")
        log(f"  - CE图: {CE_YLIM}")
        log(f"  - 变化率图: {CHANGE_RATE_YLIM}")
        
    except Exception as e:
        log(f"错误: {str(e)}")

def example_mixed_ylim():
    """混合使用自动和自定义Y轴范围的示例"""
    
    JSON_FILE_PATH = "your_battery_data.json"
    SAMPLE_NAME = "Mixed_YLim_Example"
    KCLS = [5, 15, 30]
    
    # 示例2: 混合使用自动和自定义范围
    CURRENT_YLIM = (-1.5, 0.2)      # 自定义电流范围
    DIDT_YLIM = None                 # 自动dI/dt范围
    CAPACITY_YLIM = (0.9, 1.1)      # 自定义容量范围
    CE_YLIM = None                   # 自动CE范围
    CHANGE_RATE_YLIM = (-0.03, 0.005) # 自定义变化率范围
    
    try:
        log("="*60)
        log("混合Y轴范围示例 / Mixed Y-axis Range Example")
        log("="*60)
        
        all_cycles_data, cycle_capacities, cycle_ce_values = extract_cycles_from_json(JSON_FILE_PATH)
        if all_cycles_data is None:
            return
        
        didt_cycles_data = process_dIdt_data(all_cycles_data)
        knee_points = detect_knee_points(cycle_capacities)
        
        fig = create_battery_analysis_plots(
            all_cycles_data=all_cycles_data,
            didt_cycles_data=didt_cycles_data,
            cycle_capacities=cycle_capacities,
            cycle_ce_values=cycle_ce_values,
            knee_points=knee_points,
            Kcls=KCLS,
            sample_name=SAMPLE_NAME,
            save_path=f"{SAMPLE_NAME}_mixed_ylim.png",
            current_ylim=CURRENT_YLIM,
            didt_ylim=DIDT_YLIM,
            capacity_ylim=CAPACITY_YLIM,
            ce_ylim=CE_YLIM,
            change_rate_ylim=CHANGE_RATE_YLIM,
        )
        
        log("✓ 混合Y轴范围图表创建成功!")
        log(f"应用的Y轴范围:")
        log(f"  - 电流图: {CURRENT_YLIM} (自定义)")
        log(f"  - dI/dt图: {DIDT_YLIM} (自动)")
        log(f"  - 容量图: {CAPACITY_YLIM} (自定义)")
        log(f"  - CE图: {CE_YLIM} (自动)")
        log(f"  - 变化率图: {CHANGE_RATE_YLIM} (自定义)")
        
    except Exception as e:
        log(f"错误: {str(e)}")

def main():
    """主函数"""
    print("Y轴范围功能使用示例 / Y-axis Range Functionality Examples")
    print("="*60)
    
    print("\n可用的Y轴范围参数 / Available Y-axis Range Parameters:")
    print("  - current_ylim: 电流图Y轴范围 / Current plot Y-axis range")
    print("  - didt_ylim: dI/dt图Y轴范围 / dI/dt plot Y-axis range")
    print("  - capacity_ylim: 容量图左Y轴范围 / Capacity plot left Y-axis range")
    print("  - ce_ylim: CE图右Y轴范围 / CE plot right Y-axis range")
    print("  - change_rate_ylim: 变化率图Y轴范围 / Change rate plot Y-axis range")
    
    print("\n使用方法 / Usage:")
    print("  - 设置为None: 使用自动范围 / Set to None: use automatic range")
    print("  - 设置为(min, max): 使用自定义范围 / Set to (min, max): use custom range")
    
    print("\n示例 / Examples:")
    print("  CURRENT_YLIM = (-2.0, 0.5)  # 自定义范围 / Custom range")
    print("  DIDT_YLIM = None             # 自动范围 / Automatic range")
    
    choice = input("\n选择示例 / Choose example (1: 全自定义/All custom, 2: 混合/Mixed, q: 退出/Quit): ").strip()
    
    if choice == "1":
        example_with_custom_ylim()
    elif choice == "2":
        example_mixed_ylim()
    elif choice.lower() == "q":
        print("退出 / Exiting...")
    else:
        print("无效选择 / Invalid choice")

if __name__ == "__main__":
    main()
