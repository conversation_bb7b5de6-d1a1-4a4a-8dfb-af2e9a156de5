<!DOCTYPE html>
<html class="">
  <head>
    <meta name="wechat-enable-text-zoom-em" content="true">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="color-scheme" content="light dark">
<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0,viewport-fit=cover">
<link rel="shortcut icon" type="image/x-icon" href="//res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico" reportloaderror>
<link rel="mask-icon" href="//res.wx.qq.com/a/wx_fed/assets/res/MjliNWVm.svg" color="#4C4C4C" reportloaderror>
<link rel="apple-touch-icon-precomposed" href="//res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png" reportloaderror>
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="format-detection" content="telephone=no">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="referrer" content="strict-origin-when-cross-origin">
<script  nonce="" reportloaderror>try{document.getElementsByTagName('html').item(0).style.webkitTextSizeAdjust=JSON.parse(window.__wxWebEnv.getEnv()).fontScale+'%'}catch(e){}</script>
<script type="text/javascript" nonce="" reportloaderror>
  window.logs = { pagetime: {} };
  window.logs.pagetime['html_begin'] = (+new Date());
  window.LANG = "";
</script>

    
    <script type="text/javascript" nonce="" reportloaderror>
  
  
  var WX_BJ_REPORT = window.WX_BJ_REPORT || {};
  (function(_) {
    if (_.BadJs) {
      return;
    }
   
    var BADJS_WIN_ERR = 'BadjsWindowError';
    var extend = function(source, destination) {
      for (var property in destination) {
        source[property] = destination[property]
      }
      return source
    }
    
    _.BadJs = {
      uin: 0,
      mid: "",
      view: "wap",
      _cache: {},
      _info: {},
      _hookCallback: null,
      ignorePath: true,
      throw: function(e, extData) {
        this.onError(e, extData);
        throw e;
      },
     
     
      onError: function(e, extData) {
        try {
         
          if (e.BADJS_EXCUTED == true) {
            return;
          }
          e.BADJS_EXCUTED = true;
          var data = errToData(e);
          data.uin = this.uin;
          data.mid = this.mid;
          data.view = this.view;
          data.cmdb_module = 'mmbizwap';
         
          if (!!extData) {
            data = extend(data, extData);
          }
         
          if (data.cid) {
            data.key = "[" + data.cid + "]:" + data.key;
          }
          if (window.biz) {
            data.msg += ` || biz=${window.biz} mid=${window.mid} idx=${window.idx} scene=${window.source}`;
          }
          if (data._info) {
            if (Object.prototype.toString.call(data._info) == "[object Object]") {
              data.msg += " || info:" + JSON.stringify(data._info);
            } else if (Object.prototype.toString.call(data._info) == "[object String]") {
              data.msg += " || info:" + data._info;
            } else {
              data.msg += " || info:" + data._info;
            }
          }
          if (typeof this._hookCallback == "function") {
            if (this._hookCallback(data) === false) {
              return
            }
          }
          this._send(data);
          return _.BadJs;
        } catch (e) {
          console.error(e);
        }
      },
      winErr: function(event) {
        if (event.error && event.error.BADJS_EXCUTED) {
          return;
        }
        if (event.type === 'unhandledrejection') {
          if (event.reason instanceof Error) {
            var e = event.reason || {};
            _.BadJs.onError(createError(event.type, e.message || "", e.sourceURL || "", e.line || "", e.column || "", event.reason));
          } else {
            var message = typeof event.reason === 'object' ? JSON.stringify(event.reason) : event.reason;
            _.BadJs.onError(createError(event.type, message, "", "", "", event.reason));
          }
        }else{
          _.BadJs.onError(createError(BADJS_WIN_ERR, event.message, event.filename, event.lineno, event.colno, event.error));
        }
      },
      init: function(uin, mid, view) {
        this.uin = uin || this.uin;
        this.mid = mid || this.mid;
        this.view = view || this.view;
        return _.BadJs;
      },
     
      hook: function(fn) {
        this._hookCallback = fn;
        return _.BadJs;
      },
      _send: function(data) {
       
        if (!data.mid) {
          if (typeof window.PAGE_MID !== 'undefined' && window.PAGE_MID) {
            data.mid = window.PAGE_MID;
          } else {
            return;
          }
        }
        if (!data.uin) {
          data.uin = window.user_uin || 0;
        }
       
        var flag = [data.mid, data.name, data.key].join("|");
        if (this._cache && this._cache[flag]) {
          return
        } else {
          this._cache && (this._cache[flag] = true);
          this._xhr(data);
        }
        return _.BadJs;
      },
      _xhr: function(data) {
       
        var xmlobj;
        if (window.ActiveXObject) {
          try {
            xmlobj = new ActiveXObject("Msxml2.XMLHTTP");
          } catch (e) {
            try {
              xmlobj = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (E) {
              xmlobj = false;
            }
          }
        } else if (window.XMLHttpRequest) {
          xmlobj = new XMLHttpRequest();
        }
        var param = "";
        for (var key in data) {
          if (key && data[key]) {
            param += [key, "=", encodeURIComponent(data[key]), "&"].join("");
          }
        }
        if (xmlobj && typeof xmlobj.open == "function") {
          xmlobj.open("POST", "https://badjs.weixinbridge.com/report", true);
          xmlobj.setRequestHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
          xmlobj.onreadystatechange = function(status) {};
          xmlobj.send(param.slice(0, -1));
        } else {
          var img = new Image();
          img.src = "https://badjs.weixinbridge.com/report?" + param;
        }
      },
     
      report: function(name, key, data) {
        this.onError(createError(name, key), data);
        return this;
      },
     
      mark: function(info) {
        this._info = extend(this._info, info);
      },
      nocache: function() {
        this._cache = false;
        return _.BadJs;
      }
    }
    function createError(name, msg, url, line, col, error) {
      return {
        name: name || "",
        message: msg || "",
        file: url || "",
        line: line || "",
        col: col || "",
        stack: (error && error.stack) || "",
      }
    }
   
    
    function errToData(e) {
      var _stack = parseStack(e);
      return {
        name: e.name,
        key: e.message,
        msg: e.message,
        stack: _stack.info,
        file: _stack.file,
        line: _stack.line,
        col: _stack.col,
        client_version: "",
        _info: e._info
      }
    }
    function parseStack(e) {
      e._info = e._info || "";
      var stack = e.stack || "";
      var _stack = {
        info: stack,
        file: e.file || "",
        line: e.line || "",
        col: e.col || "",
      };
      if (_stack.file == "") {
       
        var stackArr = stack.split(/\bat\b/);
        if (stackArr && stackArr[1]) {
          var match = /(https?:\/\/[^\n]+)\:(\d+)\:(\d+)/.exec(stackArr[1]);
          if (match) {
           
            if (match[1] && match[1] != _stack.file) {
              _stack.file && (e._info += " [file: " + _stack.file + " ]");
              _stack.file = match[1];
            }
            if (match[2] && match[2] != _stack.line) {
              _stack.line && (e._info += " [line: " + _stack.line + " ]");
              _stack.line = match[2];
            }
            if (match[3] && match[3] != _stack.col) {
              _stack.col && (e._info += " [col: " + _stack.col + " ]");
              _stack.col = match[3];
            }
          }
        }
      }
     
      if (_stack && _stack.file && _stack.file.length > 0) {
        _stack.info = _stack.info.replace(new RegExp(_stack.file.split("?")[0], "gi"), "__FILE__")
      }
     
      if (_.BadJs.ignorePath) {
        _stack.info = _stack.info.replace(/http(s)?\:[^:\n]*\//ig, "").replace(/\n/gi, "");
      }
      return _stack;
    }
   
    window.addEventListener && window.addEventListener('error', _.BadJs.winErr);
    window.addEventListener && window.addEventListener('unhandledrejection', _.BadJs.winErr);
    return _.BadJs;
  })(WX_BJ_REPORT);
  window.WX_BJ_REPORT = WX_BJ_REPORT;
  
  function __moonf__() {
    if (window.__moonhasinit) return;
    window.__moonhasinit = true;
    window.__moonclientlog = [];
    if (typeof JSON != "object") {
      window.JSON = {
        stringify: function() { return ""; },
        parse: function() { return {}; }
      };
    }
    var moon_init = function() {
      
      (function() {
        var inWx = (/MicroMessenger/i).test(navigator.userAgent);
        var inMp = (/MPAPP/i).test(navigator.userAgent);
        var _idkey = 121261;
        var _startKey;
        var _limit;
        var _badjsId;
        var _reportOpt;
        var _extInfo;
        var MOON_AJAX_NETWORK_OFFSET = 4;
        window.__initCatch = function(opt) {
          _idkey = opt.idkey;
          _startKey = opt.startKey || 0;
          _limit = opt.limit;
          _badjsId = opt.badjsId;
          _reportOpt = opt.reportOpt || "";
          _extInfo = opt.extInfo || {};
          _extInfo.rate = _extInfo.rate || 0.5;
        }
       
        window.__moon_report = function(array, rate_opt) {
          var isAcrossOrigin = false;
          var href = '';
          try {
            href = top.location.href;
          } catch (e) {
            isAcrossOrigin = true;
          }
          var rate = 0.5;
          if (!!_extInfo && !!_extInfo.rate) {
            rate = _extInfo.rate;
          }
          if (!!rate_opt && (typeof rate_opt == 'number')) {
            rate = rate_opt;
          }
          if (
            (!(/mp\.weixin\.qq\.com/).test(location.href) && !(/payapp\.weixin\.qq\.com/).test(location.href)) ||
            Math.random() > rate ||
            !(inWx || inMp) ||
            (top != window && !isAcrossOrigin && !(/mp\.weixin\.qq\.com/).test(href))
          ) {
           
          }
          if (isObject(array))
            array = [array];
          if (!isArray(array) || _idkey == '')
            return;
          var data = "";
          var log = [];
          var key = [];
          var val = [];
          var idkey = [];
         
          if (typeof _limit != "number") {
            _limit = Infinity;
          }
          for (var i = 0; i < array.length; i++) {
            var item = array[i] || {};
            if (item.offset > _limit) continue;
            if (typeof item.offset != "number") continue;
            if (item.offset == MOON_AJAX_NETWORK_OFFSET && !!_extInfo && !!_extInfo.network_rate && Math.random() >= _extInfo.network_rate) {
              continue;
            }
           
            var k = _limit == Infinity ? _startKey : (_startKey + item.offset);
            log[i] = (("[moon]" + _idkey + "_" + k + ";") + item.log + ";" + getErrorMessage(item.e || {})) || "";
            key[i] = k;
            val[i] = 1;
          }
          for (var j = 0; j < key.length; j++) {
            idkey[j] = _idkey + "_" + key[j] + "_" + val[j];
            data = data + "&log" + j + "=" + log[j];
          }
          if (idkey.length > 0) {
           
            sendReport("POST", location.protocol + '//mp.weixin.qq.com/mp/jsmonitor?', "idkey=" + idkey.join(";") + "&r=" + Math.random() + "&lc=" + log.length + data);
           
           
            var rate = 1;
            if (_extInfo && _extInfo.badjs_rate) {
              rate = _extInfo.badjs_rate;
            }
            if (Math.random() < rate) {
              data = data.replace(/uin\:(.)*\|biz\:(.)*\|mid\:(.)*\|idx\:(.)*\|sn\:(.)*\|/, '');
              if(!!_badjsId){
                var _img = new Image();
                var _src = 'https://badjs.weixinbridge.com/badjs?id=' + _badjsId + '&level=4&from=' + encodeURIComponent(location.host) + '&msg=' + encodeURIComponent(data);
                _img.src = _src.slice(0, 1024);
              }
             
              if (typeof WX_BJ_REPORT != "undefined" && WX_BJ_REPORT.BadJs) {
                for (var i = 0; i < array.length; i++) {
                  var item = array[i] || {};
                  if (item.e) {
                    WX_BJ_REPORT.BadJs.onError(item.e,{_info:item.log});
                  } else {
                    var name = /[^:;]*/.exec(item.log)[0];
                    WX_BJ_REPORT.BadJs.report(name, item.log, { mid: "mmbizwap:Monitor" });
                  }
                }
              }
            } else {
             
              for (var i = 0; i < array.length; i++) {
                var item = array[i] || {};
                if (item.e) {
                  item.e.BADJS_EXCUTED = true;
                }
              }
            }
          }
        }
        function isArray(obj) {
          return Object.prototype.toString.call(obj) === '[object Array]';
        }
        function isObject(obj) {
          return Object.prototype.toString.call(obj) === '[object Object]';
        }
        function getErrorMessage(e) {
          var stack = e.stack + ' ' + e.toString() || "";
          try {
           
            if (!window.testenv_reshost) {
              stack = stack.replace(/http(s)?:\/\/res\.wx\.qq\.com/g, "");
            } else {
              var host = 'http(s)?://' + window.testenv_reshost;
              var reg = new RegExp(host, 'g');
              stack = stack.replace(reg, "");
            }
           
            var reg = /\/([^.]+)\/js\/(\S+?)\.js(\,|:)?/g;
            while (reg.test(stack)) {
             
              stack = stack.replace(reg, function(a, b, c, d, e, f) {
                return c + d
              });
            }
          } catch (e) {
            stack = e.stack ? e.stack : ""
          }
          var ret = [];
          for (o in _reportOpt) {
            if (_reportOpt.hasOwnProperty(o)) {
              ret.push(o + ":" + _reportOpt[o]);
            }
          }
          ret.push("STK:" + stack.replace(/\n/g, ""));
          return ret.join("|");
        }
        function sendReport(type, url, data) {
          if (!/^mp\.weixin\.qq\.com$/.test(location.hostname)) {
            var tmp = [];
            data = data.replace(location.href, (location.origin || "") + (location.pathname || "")).replace("#wechat_redirect", "").replace("#rd", "").split("&");
            for (var i = 0, il = data.length; i < il; i++) {
              var a = data[i].split("=");
              if (!!a[0] && !!a[1]) {
                tmp.push(a[0] + "=" + encodeURIComponent(a[1]));
              }
            }
            var _img = new window.Image();
            _img.src = (url + tmp.join("&")).substr(0, 1024);
            return;
          }
          var xmlobj;
          if (window.ActiveXObject) {
            try {
              xmlobj = new ActiveXObject("Msxml2.XMLHTTP");
            } catch (e) {
              try {
                xmlobj = new ActiveXObject("Microsoft.XMLHTTP");
              } catch (E) {
                xmlobj = false;
              }
            }
          } else if (window.XMLHttpRequest) {
            xmlobj = new XMLHttpRequest();
          }
          if (!xmlobj) return;
         
          xmlobj.open(type, url, true);
          xmlobj.setRequestHeader("cache-control", "no-cache");
          xmlobj.setRequestHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
          xmlobj.setRequestHeader("X-Requested-With", "XMLHttpRequest");
          xmlobj.send(data);
        }

      })();
     
    };
    moon_init();
   
    (!!window.__moon_initcallback) && (window.__moon_initcallback());
  }
 
 
 
 
  __moonf__();
  
  if (!!window.addEventListener){
    window.addEventListener("load",function(){
      var MOON_SCRIPT_ERROR_KEY_OFFSET = 1;
      var ns = document.querySelectorAll("[reportloaderror]");
      for(var ni=0,nl=ns.length;ni<nl;ni++)
        ns[ni].onerror=function(ev){
          window.__moon_report([{ offset: MOON_SCRIPT_ERROR_KEY_OFFSET, log: "load_script_error:" + ev.target.src, e: new Error('LoadResError') }], 1);
          window.WX_BJ_REPORT.BadJs.report("load_script_error", ev.target.src, { mid: "mmbizwap:Monitor" });
        };
    });
  }
  </script>
   
    
    <title></title>
    <script  nonce="" reportloaderror>
    (() => {
     
      const ua = navigator.userAgent;
      const noMobile = !(/(iPhone|iPad|iPod|iOS)/i.test(ua) || /Windows\sPhone/i.test(ua) || /(Android)/i.test(ua));
      setTimeout(() => {
        noMobile && document.title === '' && (document.title = '微信公众平台');
      }, 1000);

     
      window.addEventListener('securitypolicyviolation', (e) => {
       
        if (e.effectiveDirective === 'base-uri') {
          (new Image()).src = location.protocol + '//mp.weixin.qq.com/mp/jsmonitor?idkey=523105_1_1&r=' + Math.random();
        }

        const reportData = {
          violatedDirective: e.effectiveDirective,
          blockedURI: e.blockedURI,
          sourceFile: e.sourceFile,
          lineNumber: e.lineNumber,
          columnNumber: e.columnNumber,
          disposition: e.disposition,
          referrer: e.referrer,
          sample: e.sample,
          originalPolicy: e.originalPolicy
        };
        let str = '';
        const keys = Object.keys(reportData);
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i];
          str += key + ': ' + reportData[key] + ' || ';
        }
        str += 'location: ' + location.href;

        window.WX_BJ_REPORT
        && window.WX_BJ_REPORT.BadJs
        && window.WX_BJ_REPORT.BadJs.report(
          e.effectiveDirective,
          str,
          {
            mid: 'mmbizwap:csp_report',
            view: 'wap_business'
          }
        );
      });
    })();
    </script>
    
<link rel="stylesheet" type="text/css" href="//res.wx.qq.com/t/wx_fed/weui-source/res/2.6.4/weui.min.css" reportloaderror>

<style>
body.fullscreen-padding .weui-msg {
  padding-top: calc(48px + var(--normal-top-insets));
  &::before {
    position: fixed;
    content: '';
    top: 0;
    left: 0;
    width: 100vw;
    height: var(--normal-top-insets);
    background: var(--weui-BG-2);
    z-index: 500;
  }
}
</style>

    
<script type="module" nonce="" reportloaderror>import.meta.url;import("_").catch(()=>1);async function* g(){};window.__vite_is_modern_browser=true;</script>
<script type="module" nonce="" reportloaderror>!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
<script type="module" crossorigin src="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/error.memtrz81384bb8ba.js" nonce="" reportloaderror></script>
<link rel="modulepreload" crossorigin href="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/modulepreload-polyfill.memtrz814abee2a4.js" reportloaderror>
<link rel="modulepreload" crossorigin href="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/private_share.memtrz81b55a93b4.js" reportloaderror>
<link rel="modulepreload" crossorigin href="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/comm_utils.memtrz816743b3b1.js" reportloaderror>
<link rel="modulepreload" crossorigin href="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/page_utils.memtrz811e953492.js" reportloaderror>
<link rel="modulepreload" crossorigin href="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/background_color.memtrz81d3989225.js" reportloaderror>
<link rel="modulepreload" crossorigin href="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/rec_report_key.memtrz81b768f805.js" reportloaderror>
<link rel="modulepreload" crossorigin href="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/set_article_read.memtrz81903fea98.js" reportloaderror>
<link rel="stylesheet" href="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/msg.memtrz812c2f26e4.css" reportloaderror>

    <style>
      .cooldown_tips {
        margin: 30px auto;
        margin-top: 0;
        display: flex;
        align-items: center;
        padding: 10px;
        font-size: 14px;
        background-color: #f6f7f9;
        border-radius: 6px;
      }
      .cooldown_tips_icon {
        display: block;
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }
    </style>
  </head>

  <body  id="activity-detail" class="zh_CN wx_wap_page   discuss_tab appmsg_skin_default appmsg_style_default ">
    
    <link rel="dns-prefetch" href="//res.wx.qq.com" reportloaderror>
<link rel="dns-prefetch" href="//mmbiz.qpic.cn" reportloaderror>
<link rel="dns-prefetch" href="//mpcdn.qpic.cn" reportloaderror>
<link rel="dns-prefetch" href="//mpcdn.weixin.qq.com" reportloaderror>
<link rel="dns-prefetch" href="//file.daihuo.qq.com" reportloaderror>
<link rel="dns-prefetch" href="//wxa.wxs.qq.com" reportloaderror>
<link rel="shortcut icon" type="image/x-icon" href="//res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico" reportloaderror>
<link rel="mask-icon" href="//res.wx.qq.com/a/wx_fed/assets/res/MjliNWVm.svg" color="#4C4C4C" reportloaderror>
<link rel="apple-touch-icon-precomposed" href="//res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png" reportloaderror>
<script type="text/javascript" nonce="" reportloaderror>
String.prototype.html = function (encode) {
  var replace = ["&#39;", "'", "&quot;", '"', "&nbsp;", " ", "&gt;", ">", "&lt;", "<", "&yen;", "¥", "&amp;", "&"];
 
 
 
 
 
  
  var replaceReverse = ["&", "&amp;", "¥", "&yen;", "<", "&lt;", ">", "&gt;", " ", "&nbsp;", '"', "&quot;", "'", "&#39;"];
  var target;
  if (encode) {
    target = replaceReverse;
  } else {
    target = replace;
  }
  for (var i = 0, str = this; i < target.length; i += 2) {
    str = str.replace(new RegExp(target[i], 'g'), target[i + 1]);
  }
  return str;
};

window.isInWeixinApp = function () {
  return /MicroMessenger/.test(navigator.userAgent);
};

window.getQueryFromURL = function (url) {
  url = url || 'http://qq.com/s?a=b#rd';
  var tmp = url.split('?'),
    query = (tmp[1] || "").split('#')[0].split('&'),
    params = {};
  for (var i = 0; i < query.length; i++) {
    var arg = query[i].split('=');
    params[arg[0]] = arg[1];
  }
  if (params['pass_ticket']) {
    params['pass_ticket'] = encodeURIComponent(params['pass_ticket'].html(false).html(false).replace(/\s/g, "+"));
  }
  return params;
};


(function () {
  var params = getQueryFromURL(location.href);
  window.uin = params['uin'] || "" || '';
  window.key = params['key'] || "" || '';
  window.wxtoken = params['wxtoken'] || '';
  window.pass_ticket = params['pass_ticket'] || '';
  window.appmsg_token = "" || "";

  var ua = navigator.userAgent;
  var isWin = ua.match(/Windows(\s+\w+)?\s+?(\d+\.\d+)/);
  if (ua.match(/Mac\sOS\sX\s(\d+[\.|_]\d+)/) || isWin || ua.match(/Linux\s/)) {
    document.body.classList.add('pages_skin_pc');
    if (isWin) {
      document.body.classList.add('pages_skin_windows');
    }
  }
  if (/MPAPP\/([\d\.]+)/i.test(ua)) {
    document.body.classList.add('pages_skin_mpapp');
  }
  if (params['uninteractive']) {
    document.body.classList.add('pages_skin_browser_underline');
  }
})();
</script>
<script type="text/javascript" nonce="" reportloaderror>
window.__ajaxTransferConfig = {};
</script>
    <div class="fullscreen-layout-padding" id="js_fullscreen_layout_padding">
  <div class="fullscreen-layout-padding__content">
    
  </div>
</div>
    
<script type="text/javascript" nonce="" reportloaderror>var __INLINE_SCRIPT__ = (function (exports) {
  'use strict';

  function _typeof(obj) {
    "@babel/helpers - typeof";

    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) {
      return typeof obj;
    } : function (obj) {
      return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
    }, _typeof(obj);
  }

  function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it["return"] != null) it["return"](); } finally { if (didErr) throw err; } } }; }
  function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
  function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
  
  
  var ua = navigator.userAgent;
  var is_ios = /(iPhone|iPad|iPod|iOS)/i.test(ua);
  var is_wp = /Windows\sPhone/i.test(ua);
  var is_android = /(Android)/i.test(ua);
  var is_wechat = /MicroMessenger\/([\d\.]+)/i.test(ua);
  var is_mac = /mac\sos/i.test(ua) && !is_ios;
  var is_windows = /windows\snt/i.test(ua) && !is_wp;
  var is_mpapp = /MPAPP\/([\d\.]+)/i.test(ua);
  var is_ipad = /iPad/i.test(ua);
  var is_windows_wechat = /WindowsWechat/i.test(ua);
  var is_mac_wechat = /MacWechat/i.test(ua) || /wechat.*mac os/i.test(ua);
  var is_prefetch = is_wechat && window.WeixinPrefecherJSBridge;
  var is_donut_app = /SAAASDK/i.test(ua);
  var is_harmony = /OpenHarmony|ArkWeb/i.test(ua);
  var is_linux = /Linux\s/i.test(ua);
  var is_in_miniProgram = is_android && /miniprogram/.test(ua.toLowerCase()) || window.__wxjs_environment == 'miniprogram';
  var is_wx_work = /wxwork/i.test(ua);
  function getUrlParams() {
    var vars = location.search.substring(1).split('&');
    var params = {};
    var _iterator = _createForOfIteratorHelper(vars),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var ele = _step.value;
        var pair = ele.split('=');
        var key = decodeURIComponent(pair[0]);
        if (typeof params[key] === 'undefined') {
          params[key] = decodeURIComponent(pair[1]);
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    return params;
  }
  function get() {
    var reg = /MicroMessenger\/([\d\.]+)/i;
    var ret = ua.match(reg);
    if (ret && ret[1]) {
      return ret[1];
    }
    return false;
  }
  function getMac() {
    var reg = /MacWechat\/([\d\.]+)/i;
    var ret = ua.match(reg);
    if (ret && ret[1]) {
      return ret[1];
    }
    return false;
  }
  function getMacOS() {
    var reg = /Mac OS X ([\d_]+)/i;
    var ret = ua.match(reg);
    if (ret && ret[1]) {
      return ret[1].replace(/_/g, '.');
    }
    return false;
  }
  function getWindows() {
    var reg = /WindowsWechat\(0x(.+?)\)/i;
    var ret = ua.match(reg);
    if (ret && ret[1]) {
      return ret[1];
    }
    return false;
  }
  function getWxWork() {
    var reg = /wxwork\/([\d\.]+)/i;
    var ret = ua.match(reg);
    if (ret && ret[1]) {
      return ret[1];
    }
    return false;
  }
  function getMpApp() {
    var appVersion = [2, 4, 5];
    var match = navigator.userAgent.match(/MPAPP\/(\d+(\.\d+)*)/);
    if (match) {
      appVersion = match[1].split('.').map(function (v) {
        return Number(v);
      });
    }
    return appVersion.join('.');
  }
  function getUnifiedPcVer() {
    var versionInfo = navigator.userAgent.match(/UnifiedPC\w+Wechat\(0xf\w{2}(\w+?)\w{2}\)/);
    if (versionInfo && versionInfo.length === 2) {
      var version = versionInfo[1];
      var mainVersion = getVersionNumber(version.slice(0, 1));
      var subVersion = getVersionNumber(version.slice(1, 2));
      var subVersion2 = getVersionNumber(version.slice(2, 3));
      return [mainVersion, subVersion, subVersion2].join('.');
    }
  }
  function getVersionNumber(hexStr) {
    return Number(Number("0x".concat(hexStr)).toString(10));
  }
  function getWindowsVersionFormat() {
    var versionInfo = navigator.userAgent.match(/WindowsWechat\(0x(\w+?)\)/);
    if (versionInfo && versionInfo.length === 2) {
      var version = versionInfo[1];
      var mainVersion = getVersionNumber(version.slice(1, 2));
      var subVersion = getVersionNumber(version.slice(2, 4));
      var subVersion2 = getVersionNumber(version.slice(4, 6));
      return [mainVersion, subVersion, subVersion2].join('.');
    }
    return false;
  }
  function getInner() {
    var reg = /MicroMessenger\/[\d\.]+\(0x(.+?)\)/i;
    var ret = ua.match(reg);
    if (ret && ret[1] && ret[1] != null) {
      return ret[1];
    }
    if (!ret && /MicroMessenger\/[\d\.]+/i.test(ua)) {
      var urlParams = getUrlParams();
      if (urlParams.version) {
        return urlParams.version;
      }
    }
    return false;
  }
  var opfunc = {
    'cp-1': function cp1(a, b) {
      return a < b;
    },
    cp0: function cp0(a, b) {
      return a === b;
    },
    cp1: function cp1(a, b) {
      return a > b;
    }
  };
  function cpVersion(ver, op, canEq, type) {
    var mmver = false;
    switch (type) {
      case 'mac':
        mmver = getMac();
        break;
      case 'windows':
        mmver = getWindowsVersionFormat();
        break;
      case 'wxwork':
        mmver = getWxWork();
        break;
      case 'mpapp':
        mmver = getMpApp();
        break;
      case 'unifiedpc':
        mmver = getUnifiedPcVer();
        break;
      default:
        mmver = get();
        break;
    }
    if (!mmver) {
      return;
    }
    var mmversion = mmver.split('.');
    var version = ver.split('.');
    if (!/\d+/g.test(mmversion[mmversion.length - 1])) {
      mmversion.pop();
    }
    for (var i = 0, len = Math.max(mmversion.length, version.length); i < len; ++i) {
      var mmv = mmversion[i] || '';
      var v = version[i] || '';
      var mmvn = parseInt(mmv, 10) || 0;
      var vn = parseInt(v, 10) || 0;
      var eq = opfunc.cp0(mmvn, vn);
      if (eq) {
        continue;
      }
      var cp = opfunc["cp".concat(op)];
      return cp(mmvn, vn);
    }
    return canEq || op === 0;
  }
  function eqVersion(version) {
    return cpVersion(version, 0);
  }
  function gtVersion(version, canEq) {
    return cpVersion(version, 1, canEq);
  }
  function ltVersion(version, canEq) {
    return cpVersion(version, -1, canEq);
  }
  function getPlatform() {
    if (is_ios) {
      return 'ios';
    }
    if (is_android) {
      return 'android';
    }
    if (is_mac) {
      return 'mac_os';
    }
    if (is_windows) {
      return 'windows';
    }
    return 'unknown';
  }
  var is_google_play = false;
  var inner_ver_for_google_play_check = getInner();
  if (is_android && inner_ver_for_google_play_check) {
    var v = "0x".concat(inner_ver_for_google_play_check.substr(-2));
    if (parseInt(v) >= 64 && parseInt(v) <= 79) {
      is_google_play = true;
    }
  }
  function compareHexVersion(hexNum) {
    var innerVersion = getInner();
    if (innerVersion && hexNum) {
      if (typeof hexNum === 'string') {
        hexNum = parseInt(hexNum, 16);
      }
      var version = parseInt(innerVersion, 16);
      return version >= hexNum;
    }
    return false;
  }
  var mmversion = {
    get: get,
    getMac: getMac,
    getMacOS: getMacOS,
    getWindows: getWindows,
    getInner: getInner,
    getWxWork: getWxWork,
    getMpApp: getMpApp,
    cpVersion: cpVersion,
    eqVersion: eqVersion,
    gtVersion: gtVersion,
    ltVersion: ltVersion,
    getPlatform: getPlatform,
    getVersionNumber: getVersionNumber,
    isWp: is_wp,
    isIOS: is_ios,
    isAndroid: is_android,
    isHarmony: is_harmony,
    isHarmonyWechat: is_harmony && is_wechat && cpVersion('1.0.0', 1, true),
    isInMiniProgram: is_in_miniProgram,
    isWechat: is_wechat,
    isMac: is_mac,
    isWindows: is_windows,
    isLinux: is_linux,
    isMacWechat: is_mac_wechat,
    isWindowsWechat: is_windows_wechat,
    isWxWork: is_wx_work,
    isOnlyWechat: is_wechat && !is_wx_work,
    isMpapp: is_mpapp,
    isNewMpApp: false,
    isIPad: is_ipad,
    isGooglePlay: is_google_play,
    isPrefetch: is_prefetch,
    isDonutAPP: is_donut_app,
    compareHexVersion: compareHexVersion
  };

  var initJsBridge = false;
  if (!window.JSAPIEventCallbackMap) {
    window.JSAPIEventCallbackMap = {};
  }
  function connectWebViewJavascriptBridge(callback) {
    if (window.WebViewMPapp || window.WebViewJavascriptBridge) {
      return callback(window.WebViewMPapp || window.WebViewJavascriptBridge);
    }
    if (window.WVJBCallbacks) {
      return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    if (!initJsBridge) {
      initJsBridge = true;
      var WVJBIframe = document.createElement('iframe');
      WVJBIframe.style.display = 'none';
      WVJBIframe.src = 'https://__bridge_loaded__';
      document.body.appendChild(WVJBIframe);
      setTimeout(function () {
        initJsBridge = false;
        document.body.removeChild(WVJBIframe);
      }, 0);
    }
    return false;
  }
  function invoke$1(jsapiName, opt, callback) {
    connectWebViewJavascriptBridge(function (bridge) {
      try {
        if (typeof opt === 'function') {
          callback = opt;
        }
        if (_typeof(opt) !== 'object' && typeof opt !== 'string') {
          opt = {};
        }
        bridge.callHandler(jsapiName, opt, function (res) {
          try {
            var ret = _typeof(res) === 'object' ? res : JSON.parse(res);
            var errMsg = ret.err_msg || ret.errMsg;
            console.info("[mpapp jsapi] invoke->".concat(jsapiName, " ").concat(opt.action || '', " ").concat(errMsg));
            typeof callback === 'function' && callback(ret);
          } catch (e) {
            window.WX_BJ_REPORT.BadJs.report('invoke', "callback ".concat(jsapiName, " error:"), {
              mid: 'mmbizwebapp:js_brridge',
              _info: e
            });
            console.error("[mpapp jsapi] ".concat(jsapiName, " ").concat(opt.action || ''), e, res);
          }
        });
      } catch (e) {
        window.WX_BJ_REPORT.BadJs.report('invoke', 'callback error:', {
          mid: 'mmbizwebapp:js_brridge',
          _info: e
        });
        console.error('[mpapp jsapi]', e);
      }
    });
  }

  var doc = {};
  var isAcrossOrigin = false;
  var notFoundedMPPageAction = [];
  var __moon_report = window.__moon_report || function () {};
  var MOON_JSAPI_KEY_OFFSET = 8;
  try {
    doc = top.window.document;
  } catch (e) {
    isAcrossOrigin = true;
  }
  if (!window.JSAPIEventCallbackMap) {
    window.JSAPIEventCallbackMap = {};
  }
  function ready(onBridgeReady) {
    var bridgeReady = function bridgeReady() {
      try {
        if (onBridgeReady) {
          window.onBridgeReadyTime = window.onBridgeReadyTime || Date.now();
          onBridgeReady();
        }
      } catch (e) {
        __moon_report([{
          offset: MOON_JSAPI_KEY_OFFSET,
          log: 'ready',
          e: e
        }]);
        throw e;
      }
      window.jsapiReadyTime = Date.now();
    };
    if (!isAcrossOrigin && (typeof top.window.WeixinJSBridge === 'undefined' || !top.window.WeixinJSBridge.invoke)) {
      if (doc.addEventListener) {
        doc.addEventListener('WeixinJSBridgeReady', bridgeReady, false);
      } else if (doc.attachEvent) {
        doc.attachEvent('WeixinJSBridgeReady', bridgeReady);
        doc.attachEvent('onWeixinJSBridgeReady', bridgeReady);
      }
    } else {
      bridgeReady();
    }
  }
  var invokeNotWaitA8key = ['notifyPageInfo'];
  var checkNotFoundedInvoke = function checkNotFoundedInvoke(methodName, args) {
    if (methodName === 'handleMPPageAction' && (args === null || args === void 0 ? void 0 : args.action) && notFoundedMPPageAction.includes(args === null || args === void 0 ? void 0 : args.action)) {
      return true;
    }
    return false;
  };
  function invoke(methodName, args, callback) {
    if (!invokeNotWaitA8key.includes(methodName) && window.__second_open_wait_a8key__ && window.__second_open_wait_a8key_task__) {
      window.__second_open_wait_a8key_task__.push(function () {
        invoke(methodName, args, callback);
      });
      return;
    }
    ready(function () {
      if (isAcrossOrigin) return false;
      if (_typeof(top.window.WeixinJSBridge) !== 'object') {
        alert('请在微信中打开此链接');
        return false;
      }
      if (checkNotFoundedInvoke(methodName, args)) {
        setTimeout(function () {
          if (callback) {
            callback.apply(window, [{
              err_msg: "".concat(methodName, ":fail"),
              err_desc: 'action isn\'t supported'
            }]);
          }
        }, 0);
      } else {
        top.window.WeixinJSBridge.invoke(methodName, args, function () {
          try {
            for (var _len = arguments.length, rets = new Array(_len), _key = 0; _key < _len; _key++) {
              rets[_key] = arguments[_key];
            }
            var ret = rets[0];
            var errMsg = ret && ret.err_msg ? ", err_msg-> ".concat(ret.err_msg) : '';
            if (['handleMPPageAction', 'handleVideoAction', 'handleHaokanAction'].indexOf(methodName) !== -1) {
              var action = (args === null || args === void 0 ? void 0 : args.action) || '';
              console.info('[system]', "[jsapi] invoke->".concat(methodName, ", action->").concat(action).concat(errMsg));
            } else {
              console.info('[system]', "[jsapi] invoke->".concat(methodName).concat(errMsg));
            }
            if (methodName === 'handleMPPageAction' && (args === null || args === void 0 ? void 0 : args.action) && (ret === null || ret === void 0 ? void 0 : ret.err_desc) === 'action isn\'t supported') {
              notFoundedMPPageAction.push(args === null || args === void 0 ? void 0 : args.action);
            }
            if (callback) {
              callback.apply(window, rets);
            }
          } catch (e) {
            __moon_report([{
              offset: MOON_JSAPI_KEY_OFFSET,
              log: "invoke;methodName:".concat(methodName),
              e: e
            }]);
            throw e;
          }
        });
      }
    });
  }
  function call(methodName) {
    if (window.__second_open_wait_a8key__ && window.__second_open_wait_a8key_task__) {
      window.__second_open_wait_a8key_task__.push(function () {
        call(methodName);
      });
      return;
    }
    ready(function () {
      if (isAcrossOrigin) return false;
      if (_typeof(top.window.WeixinJSBridge) !== 'object') {
        return false;
      }
      try {
        top.window.WeixinJSBridge.call(methodName);
      } catch (e) {
        __moon_report([{
          offset: MOON_JSAPI_KEY_OFFSET,
          log: "call;methodName:".concat(methodName),
          e: e
        }]);
        throw e;
      }
    });
  }
  function on(eventName, callback) {
    if (window.__second_open_wait_a8key__ && window.__second_open_wait_a8key_task__) {
      window.__second_open_wait_a8key_task__.push(function () {
        on(eventName, callback);
      });
      return;
    }
    ready(function () {
      if (isAcrossOrigin) return false;
      if (_typeof(top.window.WeixinJSBridge) !== 'object' || !top.window.WeixinJSBridge.on) {
        return false;
      }
      if (!window.JSAPIEventCallbackMap[eventName]) {
        window.JSAPIEventCallbackMap[eventName] = [];
      }
      window.JSAPIEventCallbackMap[eventName].push(callback);
      if (window.JSAPIEventCallbackMap[eventName].length > 1) {
        return false;
      }
      top.window.WeixinJSBridge.on(eventName, function () {
        try {
          for (var _len2 = arguments.length, rets = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            rets[_key2] = arguments[_key2];
          }
          var ret = rets[0];
          var errMsg = ret && ret.err_msg ? ", err_msg-> ".concat(ret.err_msg) : '';
          console.info('[system]', "[jsapi] event->".concat(eventName).concat(errMsg));
          if (window.JSAPIEventCallbackMap[eventName] && window.JSAPIEventCallbackMap[eventName].length) {
            var result;
            for (var i = 0; i < window.JSAPIEventCallbackMap[eventName].length; i++) {
              result = window.JSAPIEventCallbackMap[eventName][i].apply(window, rets);
            }
            return result;
          }
        } catch (e) {
          __moon_report([{
            offset: MOON_JSAPI_KEY_OFFSET,
            log: "on;eventName:".concat(eventName),
            e: e
          }]);
          throw e;
        }
      });
    });
  }
  function remove(eventName, callback) {
    if (window.__second_open_wait_a8key__ && window.__second_open_wait_a8key_task__) {
      window.__second_open_wait_a8key_task__.push(function () {
        remove(eventName, callback);
      });
      return;
    }
    ready(function () {
      if (!window.JSAPIEventCallbackMap[eventName]) {
        return false;
      }
      var result = false;
      for (var i = window.JSAPIEventCallbackMap[eventName].length - 1; i >= 0; i--) {
        if (window.JSAPIEventCallbackMap[eventName][i] === callback) {
          window.JSAPIEventCallbackMap[eventName].splice(i, 1);
          result = true;
        }
      }
      return result;
    });
  }
  var JSAPI = {
    ready: ready,
    invoke: invoke,
    call: call,
    on: on,
    remove: remove
  };

  
  function _log(level, msg) {
    if (level === 'log') {
      level = 'info';
      msg = "[WechatFe]".concat(msg);
    } else {
      var prefix = "__wap__".concat(window.__second_open__ ? ' (sec)' : '');
      msg = "".concat(prefix, " ").concat(msg, " location:[").concat(location.href, "]");
    }
    msg += new Error().stack;
    if (mmversion.isMpapp) {
      invoke$1('WNNativeCallbackLog', msg);
    } else if (mmversion.isWechat) {
      if (mmversion.isAndroid) {
        console.warn('[system]', "[MicroMsg.JsApiLog][".concat(level, "] jslog : ").concat(msg));
      } else if (mmversion.isIOS) {
        JSAPI.invoke('writeLog', {
          level: level,
          msg: msg
        });
      } else {
        JSAPI.invoke('log', {
          level: level,
          msg: msg
        });
      }
    }
  }
  var Log = {
    info: function info() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _log('info', args.join(' '));
    },
    warn: function warn() {
      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
        args[_key2] = arguments[_key2];
      }
      _log('warn', args.join(' '));
    },
    error: function error() {
      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        args[_key3] = arguments[_key3];
      }
      _log('error', args.join(' '));
    },
    debug: function debug() {
      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
        args[_key4] = arguments[_key4];
      }
      _log('debug', args.join(' '));
    },
    log: function log() {
      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {
        args[_key5] = arguments[_key5];
      }
      _log('info', args.join(' '));
    }
  };

  
  
  var Device = {};
  function detect(ua) {
    var MQQBrowser = ua.match(/MQQBrowser\/(\d+\.\d+)/i);
    var MQQClient = ua.match(/QQ\/(\d+\.(\d+)\.(\d+)\.(\d+))/i) || ua.match(/V1_AND_SQ_([\d\.]+)/);
    var WeChat = ua.match(/MicroMessenger\/((\d+)\.(\d+))\.(\d+)/) || ua.match(/MicroMessenger\/((\d+)\.(\d+))/);
    var MacOS = ua.match(/Mac\sOS\sX\s(\d+[\.|_]\d+)/);
    var WinOS = ua.match(/Windows(\s+\w+)?\s+?(\d+\.\d+)/);
    var Linux = ua.match(/Linux\s/);
    var MiuiBrowser = ua.match(/MiuiBrowser\/(\d+\.\d+)/i);
    var M1 = ua.match(/MI-ONE/);
    var MIPAD = ua.match(/MI PAD/);
    var UC = ua.match(/UCBrowser\/(\d+\.\d+(\.\d+\.\d+)?)/) || ua.match(/\sUC\s/);
    var IEMobile = ua.match(/IEMobile(\/|\s+)(\d+\.\d+)/) || ua.match(/WPDesktop/);
    var ipod = ua.match(/(ipod).*\s([\d_]+)/i);
    var ipad = ua.match(/(ipad).*\s([\d_]+)/i);
    var iphone = ua.match(/(iphone)\sos\s([\d_]+)/i);
    var Chrome = ua.match(/Chrome\/(\d+\.\d+)/);
    var AndriodBrowser = ua.match(/Mozilla.*Linux.*Android.*AppleWebKit.*Mobile Safari/);
    var android = ua.match(/(android)\s([\d\.]+)/i);
    var harmony = ua.match(/(OpenHarmony)\s([\d\.]+)/i);
    Device.browser = Device.browser || {}, Device.os = Device.os || {};
    Device.os.type = -1;
    Device.os.unifiedPC = ua.match(/UnifiedPC/);
    Device.os.unifiedMac = /UnifiedPCMac/i.test(ua);
    Device.os.unifiedWindows = /UnifiedPCWindows/i.test(ua);
    if (window.ActiveXObject) {
      var vie = 6;
      (window.XMLHttpRequest || ua.indexOf('MSIE 7.0') > -1) && (vie = 7);
      (window.XDomainRequest || ua.indexOf('Trident/4.0') > -1) && (vie = 8);
      ua.indexOf('Trident/5.0') > -1 && (vie = 9);
      ua.indexOf('Trident/6.0') > -1 && (vie = 10);
      Device.browser.ie = true, Device.browser.version = vie;
    } else if (ua.indexOf('Trident/7.0') > -1) {
      Device.browser.ie = true, Device.browser.version = 11;
    }
    if (android) {
      Device.os.android = true;
      Device.os.version = android[2];
      Device.os.type = 2;
    }
    if (harmony) {
      Device.os.harmony = true;
      Device.os.version = harmony[2];
      Device.os.type = 42;
    }
    if (ipod) {
      Device.os.ios = Device.os.ipod = true;
      Device.os.version = ipod[2].replace(/_/g, '.');
    }
    if (ipad) {
      Device.os.ios = Device.os.ipad = true;
      Device.os.version = ipad[2].replace(/_/g, '.');
      Device.os.type = 13;
    }
    if (iphone) {
      Device.os.iphone = Device.os.ios = true;
      Device.os.version = iphone[2].replace(/_/g, '.');
      Device.os.type = 1;
    }
    if (WinOS) Device.os.windows = true, Device.os.version = WinOS[2], Device.os.type = 15;
    if (MacOS) Device.os.Mac = true, Device.os.version = MacOS[1], Device.os.type = 14;
    if (Linux) Device.os.Linux = true, Device.os.type = 33;
    if (ua.indexOf('lepad_hls') > 0) Device.os.LePad = true;
    if (MIPAD) Device.os.MIPAD = true;
    if (MQQBrowser) Device.browser.MQQ = true, Device.browser.version = MQQBrowser[1];
    if (MQQClient) Device.browser.MQQClient = true, Device.browser.version = MQQClient[1];
    if (WeChat) Device.browser.WeChat = true, Device.browser.mmversion = Device.browser.version = WeChat[1];
    if (MiuiBrowser) Device.browser.MIUI = true, Device.browser.version = MiuiBrowser[1];
    if (UC) Device.browser.UC = true, Device.browser.version = UC[1] || NaN;
    if (IEMobile) Device.browser.IEMobile = true, Device.browser.version = IEMobile[2];
    if (AndriodBrowser) {
      Device.browser.AndriodBrowser = true;
    }
    if (M1) {
      Device.browser.M1 = true;
    }
    if (Chrome) {
      Device.browser.Chrome = true, Device.browser.version = Chrome[1];
    }
    if (Device.os.windows) {
      if (typeof navigator.platform !== "undefined" && navigator.platform.toLowerCase() == "win64") {
        Device.os.win64 = true;
      } else {
        Device.os.win64 = false;
      }
    }
    if (Device.os.Mac || Device.os.windows || Device.os.Linux || Device.os.unifiedPC) {
      Device.os.pc = true;
    }
    var osType = {
      iPad7: 'iPad; CPU OS 7',
      LePad: 'lepad_hls',
      XiaoMi: 'MI-ONE',
      SonyDTV: "SonyDTV",
      SamSung: 'SAMSUNG',
      HTC: 'HTC',
      VIVO: 'vivo'
    };
    for (var os in osType) {
      Device.os[os] = ua.indexOf(osType[os]) !== -1;
    }
    Device.os.phone = Device.os.phone || /windows phone/i.test(ua);
    Device.os.getNumVersion = function () {
      return parseFloat(Device.os.version);
    };
    Device.os.hasTouch = 'ontouchstart' in window;
    if (Device.os.hasTouch && Device.os.ios && Device.os.getNumVersion() < 6) {
      Device.os.hasTouch = false;
    }
    if (Device.browser.WeChat && Device.browser.version < 5.0) {
      Device.os.hasTouch = false;
    }
    Device.browser.getNumVersion = function () {
      return parseFloat(Device.browser.version);
    };
    Device.browser.isFFCanOcx = function () {
      return !!Device.browser.firefox && Device.browser.getNumVersion() >= 3.0;
    };
    Device.browser.isCanOcx = function () {
      return !!Device.os.windows && (!!Device.browser.ie || Device.browser.isFFCanOcx() || !!Device.browser.webkit);
    };
    Device.browser.isNotIESupport = function () {
      return !!Device.os.windows && (!!Device.browser.webkit || Device.browser.isFFCanOcx());
    };
    Device.userAgent = {};
    Device.userAgent.browserVersion = Device.browser.version;
    Device.userAgent.osVersion = Device.os.version;
    if (Device.os.unifiedPC) {
      if (Device.os.unifiedWindows) Device.os.type = 37;else if (Device.os.unifiedMac) Device.os.type = 38;else Device.os.type = 39;
    }
    delete Device.userAgent.version;
  }
  detect(window.navigator.userAgent);
  function canSupportH5Video() {
    var ua = window.navigator.userAgent,
      m = null;
    if (!!Device.os.android) {
      if (Device.browser.MQQ && Device.browser.getNumVersion() >= 4.2) {
        return true;
      }
      if (ua.indexOf('MI2') != -1) {
        return true;
      }
      if (Device.os.version >= '4' && (m = ua.match(/MicroMessenger\/((\d+)\.(\d+))\.(\d+)/))) {
        if (parseFloat(m[1]) >= 4.2) {
          return true;
        }
      }
      if (Device.os.version >= '4.1') {
        return true;
      }
    }
    return false;
  }
  function canSupportVideoMp4() {
    var video = document.createElement('video');
    if (typeof video.canPlayType === 'function') {
      if (video.canPlayType('video/mp4; codecs="mp4v.20.8"') === 'probably') {
        return true;
      }
      if (video.canPlayType('video/mp4; codecs="avc1.42E01E"') === 'probably' || video.canPlayType('video/mp4; codecs="avc1.42E01E, mp4a.40.2"') === 'probably') {
        return true;
      }
    }
    return false;
  }
  function canSupportAutoPlay() {
    if (Device.os.ios && Device.os.getNumVersion() < 10) {
      return false;
    }
    return true;
  }
  function isLockdownMode() {
    if (!Device.os.ios || Device.os.getNumVersion() < 16) {
      return false;
    }
    if (typeof WebAssembly === 'undefined' && typeof OfflineAudioContext === 'undefined' && typeof WebGLRenderingContext === 'undefined') {
      return true;
    }
    return false;
  }
  Device.canSupportVideo = canSupportVideoMp4 || canSupportH5Video;
  Device.canSupportVideoMp4 = canSupportVideoMp4;
  Device.canSupportH5Video = canSupportH5Video;
  Device.canSupportAutoPlay = canSupportAutoPlay;
  Device.isLockdownMode = isLockdownMode;
  
  Device.cpVersion = function (version) {
    var cp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    var canEqual = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    var nowVersionStr = Device.os.version;
    if (!nowVersionStr) return false;
    var versionArr = version.split('.');
    var nowVersionArr = nowVersionStr.split('.');
    for (var i = 0; i < Math.max(nowVersionArr.length, versionArr.length); i++) {
      var vi = +versionArr[i];
      var nvi = +nowVersionArr[i];
      if (vi === nvi) continue;
      if (cp > 0) return vi > nvi;
      if (cp < 0) return vi < nvi;
    }
    return canEqual || cp === 0;
  };

  
  var attrs = ['top', 'left', 'right', 'bottom'];
  var elementComputedStyle = {};
  if (Device.os.android) {
    JSAPI.invoke('handleDeviceInfo', {
      action: 'getSafeAreaInsets'
    }, function (res) {
      if (res.err_msg.indexOf(':ok') !== -1) {
        elementComputedStyle.top = res.top;
        elementComputedStyle.left = res.left;
        elementComputedStyle.right = window.screen.width - res.right;
        elementComputedStyle.bottom = window.screen.height - res.bottom;
      } else {
        attrs.forEach(function (attr) {
          elementComputedStyle[attr] = 0;
        });
      }
    });
  }

  var idkey = 398384;
  var reportMap = {
    0: 7,
    5: 13,
    7: 19,
    8: 25,
    10: 31
  };
  function inWhiteList(itemShowType) {
    if ([5, 7, 8, 10].indexOf(itemShowType) > -1) return true;
    if (itemShowType === 0) {
      return mmversion.isIOS && mmversion.compareHexVersion('1800352B') || mmversion.isAndroid;
    }
    return false;
  }
  function getImmersiveMode(itemShowType) {
    if (!inWhiteList(itemShowType)) return;
    if (window !== top) {
      return;
    }
    var env = window.__wxWebEnv && typeof window.__wxWebEnv.getEnv === 'function' && window.__wxWebEnv.getEnv();
    if (env && typeof env === 'string') {
      try {
        env = JSON.parse(env);
      } catch (err) {
        env = {};
      }
    } else {
      env = {};
    }
    console.log('[env] getImmersiveMode', env, itemShowType);
    try {
      Log.info("[immersiveMode] ".concat(JSON.stringify(env || {})));
    } catch (e) {
    }
    var needChange = window.immersiveMode !== !!env.immersiveMode;
    window.immersiveMode = !!env.immersiveMode;
    if (needChange) {
      if (env.immersiveMode) {
        window.weixinPostMessageHandlers && window.weixinPostMessageHandlers.monitorHandler && typeof window.weixinPostMessageHandlers.monitorHandler.postMessage === 'function' && window.weixinPostMessageHandlers.monitorHandler.postMessage(JSON.stringify({
          event: 'stopImmersiveLoading'
        }));
        window.normalTopInset = env && typeof env.normalTopInset !== 'undefined' ? +env.normalTopInset : 91;
        document.body.classList.add('fullscreen-padding');
        document.body.style = "".concat(document.body.style, ";--normal-top-insets: ").concat(window.normalTopInset || 91, "px;");
        report(itemShowType);
      } else {
        window.normalTopInset = 0;
        document.body.classList.remove('fullscreen-padding');
      }
    }
    if (window.immersiveMode) {
      JSAPI.invoke('setNavigationBarColor', {
        wxcolor: {
          light: '#FFFFFF',
          dark: '#191919'
        },
        alpha: 0
      });
      if (mmversion.isAndroid) {
        JSAPI.invoke('disableBounceScroll', {
          'place': ['top', 'bottom']
        });
      }
    }
    return env;
  }
  var showScrollBorder = false;
  var doubleClickTemp = false;
  function registerImmersiveListener(itemShowType) {
    if (!inWhiteList(itemShowType)) return;
    if (!window.__hasRegisterImmersiveListener) {
      window.__hasRegisterImmersiveListener = true;
      JSAPI.on('activity:state_change', function () {
        return getImmersiveMode();
      });
      JSAPI.on('fakeImmersiveUIStyleTopInsetChanged', function (args) {
        console.log('[env] fakeImmersiveUIStyleTopInsetChanged', args);
        try {
          Log.info("[immersiveMode] fakeImmersiveUIStyleTopInsetChanged ".concat(JSON.stringify(args || {})));
        } catch (e) {
        }
        if (document.body.classList.contains('fullscreen-padding')) {
          window.normalTopInset = args && typeof args.top !== 'undefined' ? +args.top : window.normalTopInset;
          document.body.style.setProperty('--normal-top-insets', "".concat(window.normalTopInset, "px"));
        }
      });
      window.addEventListener('scroll', function () {
        if (!window.immersiveMode) return;
        var dom = document.getElementById('js_content_container');
        var scrollTop = dom && dom.scrollTop || window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
        if (showScrollBorder && scrollTop <= 2) {
          showScrollBorder = false;
          document.body.classList.remove('fullscreen-padding__border');
        } else if (!showScrollBorder && scrollTop > 2) {
          showScrollBorder = true;
          document.body.classList.add('fullscreen-padding__border');
        }
      });

      if (mmversion.isAndroid && mmversion.compareHexVersion('28003859')) {
        JSAPI.on('onActionBarClickEventInImmersiveMode', function (res) {
          console.log('onActionBarClickEventInImmersiveMode', res);
          var x = res.x,
            y = res.y,
            action = res.action;
          if (!document.elementsFromPoint) return;
          var nodeAtPoint = document.elementFromPoint(+x, +y);
          if (action === 'click') {
            typeof nodeAtPoint.click === 'function' && nodeAtPoint.click();
          } else if (action === 'longpress_start') {
            var touchStartEvent = new TouchEvent('touchstart', {
              bubbles: true,
              cancelable: true,
              touches: [new Touch({
                identifier: 1,
                target: nodeAtPoint,
                clientX: +x,
                clientY: +y
              })]
            });
            nodeAtPoint.dispatchEvent(touchStartEvent);
          } else if (action === 'longpress_end') {
            var touchEndEvent = new TouchEvent('touchend', {
              bubbles: true,
              cancelable: true,
              touches: []
            });
            nodeAtPoint.dispatchEvent(touchEndEvent);
          }
        });
      }
      document.body.addEventListener('click', function (e) {
        if (!window.immersiveMode) return;
        if (e.clientY <= window.normalTopInset) {
          if (doubleClickTemp) {
            window.scrollTo({
              top: 0,
              behavior: 'smooth'
            });
            doubleClickTemp = false;
          } else {
            doubleClickTemp = true;
            setTimeout(function () {
              doubleClickTemp = false;
            }, 300);
          }
        }
      });
    }
  }
  function setFullscreenWebview(itemShowType) {
    if (!inWhiteList(itemShowType)) return;
    if (window !== top) {
      return;
    }
    getImmersiveMode(itemShowType);
    registerImmersiveListener(itemShowType);
  }
  function report(itemShowType) {
    new Image().src = "https://mp.weixin.qq.com/mp/jsmonitor?idkey=".concat(idkey, "_1_1");
    if (mmversion.isAndroid) {
      new Image().src = "https://mp.weixin.qq.com/mp/jsmonitor?idkey=".concat(idkey, "_2_1");
    } else if (mmversion.isIOS) {
      new Image().src = "https://mp.weixin.qq.com/mp/jsmonitor?idkey=".concat(idkey, "_3_1");
    }
    var key = reportMap[itemShowType];
    if (key) {
      new Image().src = "https://mp.weixin.qq.com/mp/jsmonitor?idkey=".concat(idkey, "_").concat(key, "_1");
      if (mmversion.isAndroid) {
        new Image().src = "https://mp.weixin.qq.com/mp/jsmonitor?idkey=".concat(idkey, "_").concat(key + 1, "_1");
      } else if (mmversion.isIOS) {
        new Image().src = "https://mp.weixin.qq.com/mp/jsmonitor?idkey=".concat(idkey, "_").concat(key + 2, "_1");
      }
    }
  }
  if (!window.__second_open__) {
    var itemShowType = window.a_value_which_never_exists || '';
    setFullscreenWebview(+itemShowType);
  }

  exports.getImmersiveMode = getImmersiveMode;
  exports.registerImmersiveListener = registerImmersiveListener;
  exports.setFullscreenWebview = setFullscreenWebview;

  Object.defineProperty(exports, '__esModule', { value: true });

  return exports;

})({});</script>
<div class="weui-msg">
            <div class="weui-msg__icon-area">
                            <i class="weui-icon-warn weui-icon_msg"></i>
                    </div>
        <div class="weui-msg__text-area">
                    <div class="weui-msg__title warn">参数错误</div>
            </div>
    </div>

    <script type="text/javascript" nonce="" reportloaderror>
window.logs.pagetime.page_begin = Date.now();

try {
  var adIframeUrl = localStorage.getItem('__WXLS_ad_iframe_url');
  if (window === top) {
    if (adIframeUrl) {
      if (navigator.userAgent.indexOf('iPhone') > -1) {
        var img = new Image();
        img.src = adIframeUrl;
      } else {
        var link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = adIframeUrl;
        document.getElementsByTagName('head')[0].appendChild(link);
      }
    }
  }
} catch (err) {

}
</script>
    

<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_colon">：</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma0">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma1">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma2">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma3">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma4">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma5">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma6">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma7">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma8">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma9">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comma10">，</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_period">。</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_space">&nbsp;</span>


<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_type_video">视频</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_type_weapp">小程序</span>


<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_zan_btn_txt">赞</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_zan_btn_tips">，轻点两下取消赞</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_like_btn_txt">在看</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_like_btn_tips">，轻点两下取消在看</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_share_btn_txt">分享</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_comment_btn_txt">留言</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_collect_btn_txt">收藏</span>
<span aria-hidden="true" class="weui-a11y_ref" style="display:none" id="js_a11y_op_ting_heard">听过</span>

    <script type="text/javascript" nonce="" reportloaderror>
(function () {
  var totalCount = 0,
    finishCount = 0;

  function _addScript(uri, cb) {
    totalCount++;
    var node = document.createElement('SCRIPT');
    node.type = 'text/javascript';
    node.src = uri;
    node.setAttribute('nonce', '');
    if (cb) {
      node.onload = cb;
    }
    document.getElementsByTagName('head')[0].appendChild(node);
  }
  if ((document.cookie && document.cookie.indexOf('vconsole_open=1') > -1) || location.href.indexOf('vconsole=1') > -1) {
    _addScript('https://mp.weixin.qq.com/mmbizappmsg/zh_CN/htmledition/js/scripts/vconsole-3.14.6.js', function () {
      window.vConsole = new window.VConsole();
    });
  }
  if (document.cookie && document.cookie.indexOf('__xweb_remote_debug_device_token__') > -1) {
    _addScript('https://mp.weixin.qq.com/mmbizappmsg/zh_CN/htmledition/js/scripts/mprdev-0.2.5.js', function () {
      _addScript('https://mp.weixin.qq.com/mmbizappmsg/zh_CN/htmledition/js/scripts/xwebrd-0.0.2.js');
    });
  }
})();
</script>
    
    
<script type="text/javascript" nonce="" reportloaderror>
    var biz = '' || '';
    var sn = '' || '';
    var mid = '' || '' || '';
    var idx = '' || '' || '' ;

   
    var is_rumor = '' * 1;
    var norumor = '' * 1;
</script>

    


<script nomodule nonce="" reportloaderror>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
<script nomodule crossorigin id="vite-legacy-polyfill" src="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/polyfills-legacy.memtrz81c24d6690.js" nonce="" reportloaderror></script>
<script nomodule crossorigin id="vite-legacy-entry" data-src="//res.wx.qq.com/mmbizappmsg/zh_CN/htmledition/js/assets/error-legacy.memtrz811de837fa.js" nonce="" reportloaderror>System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>

  </body>
</html>


