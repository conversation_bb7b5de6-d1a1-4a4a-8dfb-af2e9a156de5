# 多尺度时频域融合特征提取技术方案

## 创新点概述

- **创新点**：传统方法仅关注时域或频域单一特征，忽略了析锂在不同时间尺度的表现
- **突破**：设计针对析锂特征的多尺度小波变换和希尔伯特-黄变换，捕获瞬态和稳态特征
- **优势**：能够同时检测快速析锂事件（毫秒级）和缓慢累积过程（循环级）

## 实现方案逻辑

### 1. 核心思想

析锂现象具有**多时间尺度特性**：
- **快速过程**（毫秒-秒级）：电化学反应动力学、局部过电位变化
- **中等过程**（分钟-小时级）：单次充放电循环内的累积效应
- **慢速过程**（天-月级）：多循环累积的结构变化和容量衰减

传统方法只看单一尺度，容易漏掉关键信息。

### 2. 技术实现架构

```python
import numpy as np
import pywt
from scipy.signal import hilbert
from PyEMD import EMD
import tensorflow as tf

class MultiscaleFeatureExtractor:
    """多尺度时频域特征提取器"""
    
    def __init__(self):
        self.wavelet_scales = np.logspace(0, 6, 50)  # 1到10^6的对数尺度
        self.emd = EMD()
        
    def extract_multiscale_features(self, current, voltage, time):
        """提取多尺度特征"""
        features = {}
        
        # 1. 小波变换特征（捕获不同频率成分）
        wavelet_features = self._extract_wavelet_features(current, voltage)
        features.update(wavelet_features)
        
        # 2. 希尔伯特-黄变换特征（捕获非线性非平稳信号）
        hht_features = self._extract_hht_features(current, voltage)
        features.update(hht_features)
        
        # 3. 多尺度熵特征（量化复杂度）
        entropy_features = self._extract_multiscale_entropy(current)
        features.update(entropy_features)
        
        return features
    
    def _extract_wavelet_features(self, current, voltage):
        """小波变换特征提取"""
        features = {}
        
        # 连续小波变换 - 分析时频特性
        cwt_current, freqs = pywt.cwt(current, self.wavelet_scales, 'morl')
        cwt_voltage, _ = pywt.cwt(voltage, self.wavelet_scales, 'morl')
        
        # 小波能量谱（不同尺度的能量分布）
        energy_spectrum = np.sum(np.abs(cwt_current)**2, axis=1)
        features['wavelet_energy_spectrum'] = energy_spectrum
        
        # 小波熵（信号复杂度）
        normalized_energy = energy_spectrum / np.sum(energy_spectrum)
        wavelet_entropy = -np.sum(normalized_energy * np.log2(normalized_energy + 1e-10))
        features['wavelet_entropy'] = wavelet_entropy
        
        # 尺度间相关性（检测析锂的多尺度耦合）
        scale_correlation = self._compute_scale_correlation(cwt_current)
        features['scale_correlation'] = scale_correlation
        
        return features
    
    def _extract_hht_features(self, current, voltage):
        """希尔伯特-黄变换特征提取"""
        features = {}
        
        # EMD分解 - 提取本征模态函数
        imfs_current = self.emd(current)
        imfs_voltage = self.emd(voltage)
        
        # 瞬时频率和振幅（捕获非平稳特性）
        for i, imf in enumerate(imfs_current[:5]):  # 取前5个IMF
            analytic_signal = hilbert(imf)
            instantaneous_amplitude = np.abs(analytic_signal)
            instantaneous_phase = np.unwrap(np.angle(analytic_signal))
            instantaneous_frequency = np.diff(instantaneous_phase) / (2 * np.pi)
            
            features[f'imf_{i}_mean_amplitude'] = np.mean(instantaneous_amplitude)
            features[f'imf_{i}_std_amplitude'] = np.std(instantaneous_amplitude)
            features[f'imf_{i}_mean_frequency'] = np.mean(instantaneous_frequency)
            
        # Hilbert谱能量分布
        hilbert_spectrum = self._compute_hilbert_spectrum(imfs_current)
        features['hilbert_spectrum_energy'] = np.sum(hilbert_spectrum, axis=0)
        
        return features
```

### 3. 关键技术说明

#### 3.1 小波变换 (Wavelet Transform)

**作用**：将信号分解到不同的时间-频率窗口
- **Morlet小波**：适合分析析锂的振荡特性
- **多尺度分析**：从高频噪声到低频趋势的全频段覆盖
- **时间定位**：精确定位析锂事件发生时刻

```python
def _compute_scale_correlation(self, cwt_coeffs):
    """计算不同尺度间的相关性"""
    n_scales = cwt_coeffs.shape[0]
    correlation_matrix = np.corrcoef(cwt_coeffs)
    
    # 提取上三角矩阵（避免重复）
    upper_triangle = correlation_matrix[np.triu_indices(n_scales, k=1)]
    
    return {
        'mean_correlation': np.mean(upper_triangle),
        'max_correlation': np.max(upper_triangle),
        'correlation_std': np.std(upper_triangle)
    }

def _detect_lithium_plating_signatures(self, cwt_coeffs, time_vector):
    """检测析锂特征信号"""
    # 析锂通常在低频段（长时间尺度）表现明显
    low_freq_indices = self.wavelet_scales > 100  # 对应较长时间尺度
    low_freq_coeffs = cwt_coeffs[low_freq_indices, :]
    
    # 寻找异常能量集中区域
    energy_density = np.sum(np.abs(low_freq_coeffs)**2, axis=0)
    threshold = np.mean(energy_density) + 2 * np.std(energy_density)
    
    anomaly_indices = np.where(energy_density > threshold)[0]
    
    return {
        'anomaly_time_points': time_vector[anomaly_indices],
        'anomaly_energy': energy_density[anomaly_indices],
        'total_anomaly_energy': np.sum(energy_density[anomaly_indices])
    }
```

#### 3.2 希尔伯特-黄变换 (Hilbert-Huang Transform, HHT)

**作用**：分析非线性、非平稳信号的瞬时特性
- **EMD分解**：自适应地将信号分解为本征模态函数(IMF)
- **瞬时频率**：捕获析锂过程中的频率变化
- **非线性特征**：检测析锂引起的非线性动力学变化

```python
def _compute_hilbert_spectrum(self, imfs):
    """计算Hilbert谱"""
    hilbert_spectrum = []
    
    for imf in imfs:
        # 计算解析信号
        analytic_signal = hilbert(imf)
        instantaneous_amplitude = np.abs(analytic_signal)
        instantaneous_phase = np.unwrap(np.angle(analytic_signal))
        instantaneous_frequency = np.diff(instantaneous_phase) / (2 * np.pi)
        
        # 构建时频表示
        hilbert_spectrum.append(instantaneous_amplitude[:-1] * instantaneous_frequency)
    
    return np.array(hilbert_spectrum)

def _analyze_imf_characteristics(self, imfs, sampling_rate):
    """分析IMF特征"""
    characteristics = {}
    
    for i, imf in enumerate(imfs):
        # 计算IMF的统计特征
        characteristics[f'imf_{i}_energy'] = np.sum(imf**2)
        characteristics[f'imf_{i}_zero_crossings'] = len(np.where(np.diff(np.sign(imf)))[0])
        
        # 计算主导频率
        fft_imf = np.fft.fft(imf)
        freqs = np.fft.fftfreq(len(imf), 1/sampling_rate)
        dominant_freq_idx = np.argmax(np.abs(fft_imf[:len(imf)//2]))
        characteristics[f'imf_{i}_dominant_frequency'] = freqs[dominant_freq_idx]
        
        # 计算IMF的规律性（用于检测析锂的周期性变化）
        autocorr = np.correlate(imf, imf, mode='full')
        autocorr = autocorr[autocorr.size // 2:]
        characteristics[f'imf_{i}_regularity'] = np.max(autocorr[1:]) / autocorr[0]
    
    return characteristics
```

### 4. 多尺度融合策略

```python
class MultiscaleFusionNetwork(tf.keras.Model):
    """多尺度特征融合网络"""
    
    def __init__(self, feature_dims):
        super().__init__()
        self.feature_dims = feature_dims
        
        # 不同尺度的特征编码器
        self.fast_scale_encoder = tf.keras.Sequential([
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(32, activation='relu')
        ])
        
        self.medium_scale_encoder = tf.keras.Sequential([
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(32, activation='relu')
        ])
        
        self.slow_scale_encoder = tf.keras.Sequential([
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(32, activation='relu')
        ])
        
        # 注意力机制融合
        self.attention_layer = tf.keras.layers.MultiHeadAttention(
            num_heads=4, key_dim=32
        )
        
        # 最终分类层
        self.classifier = tf.keras.Sequential([
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
    
    def call(self, inputs, training=None):
        # 分离不同尺度特征
        fast_features = inputs[:, :self.feature_dims['fast']]
        medium_features = inputs[:, self.feature_dims['fast']:self.feature_dims['fast']+self.feature_dims['medium']]
        slow_features = inputs[:, -self.feature_dims['slow']:]
        
        # 编码不同尺度特征
        fast_encoded = self.fast_scale_encoder(fast_features, training=training)
        medium_encoded = self.medium_scale_encoder(medium_features, training=training)
        slow_encoded = self.slow_scale_encoder(slow_features, training=training)
        
        # 堆叠特征用于注意力机制
        stacked_features = tf.stack([fast_encoded, medium_encoded, slow_encoded], axis=1)
        
        # 应用注意力机制
        attended_features = self.attention_layer(
            stacked_features, stacked_features, training=training
        )
        
        # 全局平均池化
        fused_features = tf.reduce_mean(attended_features, axis=1)
        
        # 最终预测
        output = self.classifier(fused_features, training=training)
        
        return output
```

### 5. 析锂检测的时间尺度映射

| 时间尺度 | 物理过程 | 检测方法 | 特征表现 |
|---------|---------|---------|---------|
| **毫秒-秒级** | 电化学反应动力学 | 高频小波系数 | 电流/电压瞬态响应 |
| **分钟-小时级** | 单循环累积效应 | 中频IMF分量 | dQ/dV曲线变化 |
| **天-月级** | 多循环结构变化 | 低频趋势分析 | 容量衰减模式 |

### 6. 实际应用优势

1. **早期检测**：通过多尺度分析，能在析锂累积到宏观可见之前检测到微观变化
2. **鲁棒性强**：不同尺度特征互补，减少单一特征的误判
3. **物理意义明确**：每个尺度对应具体的电化学过程，便于解释和验证

这种多尺度方法本质上是将析锂这个复杂的多物理过程在时频域进行全面"解剖"，确保不遗漏任何可能的早期征象。

## 技术名词解释

### 小波变换 (Wavelet Transform)
一种时频分析方法，能够同时提供信号的时间和频率信息。与傅里叶变换不同，小波变换具有良好的时间局部化特性，特别适合分析非平稳信号。

### 希尔伯特-黄变换 (Hilbert-Huang Transform)
由经验模态分解(EMD)和希尔伯特谱分析组成的信号处理方法，专门用于分析非线性、非平稳信号的瞬时频率特性。

### 本征模态函数 (Intrinsic Mode Function, IMF)
EMD分解得到的基本振荡模式，每个IMF代表信号在特定时间尺度上的振荡特征。

### 多尺度熵 (Multiscale Entropy)
量化信号在不同时间尺度上复杂度的指标，能够反映系统的动力学特性变化。

### 注意力机制 (Attention Mechanism)
深度学习中用于自动学习特征重要性权重的技术，能够让模型自动关注最相关的特征。