# Add this to the very top of your script, before other imports
import matplotlib

matplotlib.use("TkAgg")

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import time
import sys
import traceback
import json
import matplotlib.cm as cm
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import threading
import multiprocessing
from datetime import datetime
import logging
from scipy.signal import savgol_filter
from scipy.interpolate import interp1d

# Set up logging with rotation
LOG_FILE = "battery_analysis.log"
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format="[%(asctime)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

# Maximum log file size (10 MB)
MAX_LOG_SIZE = 10 * 1024 * 1024


def rotate_log_if_needed():
    """Rotate log file if it gets too large"""
    if os.path.exists(LOG_FILE) and os.path.getsize(LOG_FILE) > MAX_LOG_SIZE:
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        backup_log = f"{LOG_FILE}.{timestamp}"
        try:
            os.rename(LOG_FILE, backup_log)
        except Exception:
            pass  # Continue with current log if rotation fails


def log(message, text_widget=None):
    """Record log messages and display in GUI"""
    rotate_log_if_needed()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    log_message = f"[{timestamp}] {message}"
    print(log_message)
    logging.info(message)

    # Update GUI if text_widget provided
    if text_widget:
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, log_message + "\n")
        text_widget.see(tk.END)
        text_widget.config(state=tk.DISABLED)


def compute_dIdt_vectorized(time_s, current_A, internal=50):
    """Calculate dI/dt using vectorized method for better performance"""
    if len(time_s) < 2:
        return None, None

    try:
        # Convert to numpy arrays for vector operations
        time_array = np.array(time_s)
        current_array = np.array(current_A)

        # Calculate time differences and current differences
        dt = np.diff(time_array)
        dI = np.diff(current_array)

        # 大幅降低阈值，使更多数据点被保留
        # Calculate threshold - 使用更低的internal值来降低阈值
        max_current = np.max(np.abs(current_array))
        threshold = max_current / (internal * 10)  # 将阈值降低10倍

        # 只过滤掉时间差无效的点
        valid_mask = np.abs(dt) > 1e-10

        # 计算所有有效点的dI/dt
        dIdt_values = dI[valid_mask] / dt[valid_mask]
        didt_times = time_array[:-1][valid_mask]

        if len(dIdt_values) == 0:
            return None, None

        # Fix: Ensure we're not returning NaN or infinite values
        valid_values = ~np.isnan(dIdt_values) & ~np.isinf(dIdt_values)
        if not np.all(valid_values):
            dIdt_values = dIdt_values[valid_values]
            didt_times = didt_times[valid_values]

        if len(dIdt_values) == 0:
            return None, None

        # 对dI/dt值进行平滑处理，减少噪声
        if len(dIdt_values) > 5:
            window_size = min(5, len(dIdt_values) - (len(dIdt_values) % 2 == 0))
            if window_size >= 3:
                try:
                    from scipy.signal import savgol_filter

                    dIdt_values = savgol_filter(dIdt_values, window_size, 2)
                except Exception:
                    pass  # 如果平滑失败，使用原始值

        # 确保返回的是numpy数组而不是视图
        return np.array(dIdt_values), np.array(didt_times)
    except Exception as e:
        log(f"Error in compute_dIdt_vectorized: {str(e)}")
        return None, None


def compute_dIdt(time_s, current_A, internal=50, use_vectorized=True):
    """Calculate dI/dt using threshold-based method"""
    if use_vectorized:
        return compute_dIdt_vectorized(time_s, current_A, internal)

    if len(time_s) < 2:
        return None, None

    # Initialize arrays
    dIdt = []
    time_diff = []

    try:
        # Convert to numpy arrays for better performance
        time_array = time_s.values
        current_array = current_A.values

        # Calculate time span and threshold
        max_current = np.max(np.abs(current_array))
        threshold = max_current / internal

        i = 0  # Current start index
        while i < len(time_array) - 1:
            # Find step size n
            n = 1
            while (i + n < len(time_array)) and (
                np.abs(current_array[i + n] - current_array[i]) <= threshold
            ):
                n += 1
                # Safety check to avoid infinite loop
                if n > 100:
                    break

            # Prevent index out of bounds
            n = min(n, len(time_array) - i - 1)

            if n > 0:  # Ensure valid step size
                denominator = time_array[i + n] - time_array[i]
                if abs(denominator) > 1e-10:  # Avoid division by very small numbers
                    dIdt_value = (current_array[i + n] - current_array[i]) / denominator
                    dIdt.append(dIdt_value)
                    time_diff.append(time_array[i])

            # Move to next point
            i += max(1, n // 2)

    except Exception as e:
        log(f"Error in compute_dIdt: {str(e)}")
        return None, None

    # Check calculation results
    if len(dIdt) == 0:
        return None, None

    return dIdt, time_diff


def process_dIdt_data(
    all_cycles_data, text_widget=None, downsample_factor=1, use_multiprocessing=False
):
    """Process all cycle data, calculate dI/dt with optional downsampling and multiprocessing"""
    log("Calculating dI/dt for all cycles...", text_widget)
    didt_cycles = {}

    # Check if multiprocessing should be used
    if use_multiprocessing and len(all_cycles_data) > 5:
        return process_dIdt_data_parallel(
            all_cycles_data, text_widget, downsample_factor
        )

    # Create a progress tracking function for UI updates
    total_cycles = len(all_cycles_data)
    processed_cycles = 0

    for cycle, data in all_cycles_data.items():
        # Apply downsampling if requested
        if downsample_factor > 1:
            indices = np.arange(0, len(data), downsample_factor)
            if len(indices) > 0:
                data = data.iloc[indices].copy()

        # Extract time and current data
        time_s = data["Time(s)"]
        current_A = data["Current(A)"]

        # Calculate dI/dt
        didt_values, didt_times = compute_dIdt(time_s, current_A, use_vectorized=True)

        if didt_values is not None and didt_times is not None:
            # Create dI/dt dataframe
            df_didt = pd.DataFrame(
                {"Time(s)": didt_times, "dI/dt_raw(A/s)": didt_values}
            )

            # Add smoothed dI/dt column (using rolling window of 5)
            df_didt["dI/dt_smooth(A/s)"] = (
                df_didt["dI/dt_raw(A/s)"].rolling(window=5, center=True).mean()
            )

            # Fill NaN values (at beginning and end of window)
            df_didt["dI/dt_smooth(A/s)"] = df_didt["dI/dt_smooth(A/s)"].fillna(
                df_didt["dI/dt_raw(A/s)"]
            )

            # Store processed data
            didt_cycles[cycle] = df_didt

            log(
                f"  Processed cycle {cycle}: calculated {len(df_didt)} dI/dt data points",
                text_widget,
            )
        else:
            log(f"  Warning: Unable to calculate dI/dt for cycle {cycle}", text_widget)

        # Update progress
        processed_cycles += 1
        if processed_cycles % 10 == 0 or processed_cycles == total_cycles:
            progress = (processed_cycles / total_cycles) * 100
            log(
                f"  Progress: {progress:.1f}% ({processed_cycles}/{total_cycles} cycles)",
                text_widget,
            )

    log(f"dI/dt calculation complete, processed {len(didt_cycles)} cycles", text_widget)
    return didt_cycles


# Function for processing a single cycle (to be used with multiprocessing)


def process_cycle(cycle_data):
    cycle_num, data, downsample_factor = cycle_data

    # Apply downsampling if requested
    if downsample_factor > 1:
        indices = np.arange(0, len(data), downsample_factor)
        if len(indices) > 0:
            data = data.iloc[indices].copy()

    # Extract time and current data
    time_s = data["Time(s)"]  # 这里已经是秒为单位的时间
    current_A = data["Current(A)"]
    
    # 简化调试信息，只在第一个循环或特定循环输出
    if cycle_num <= 3 or cycle_num % 50 == 0:
        print(f"Processing cycle {cycle_num}: {len(data)} data points, time range: {time_s.min():.1f}s - {time_s.max():.1f}s")

    # Calculate dI/dt
    didt_values, didt_times = compute_dIdt_vectorized(time_s, current_A)

    if didt_values is not None and didt_times is not None:
        # 只在第一个循环或特定循环输出调试信息
        if cycle_num <= 3 or cycle_num % 50 == 0:
            print(f"  Cycle {cycle_num}: {len(didt_values)} dI/dt points, range: {min(didt_values):.3f} to {max(didt_values):.3f} A/s")
        
        # 确保时间单位是秒
        # Create dI/dt dataframe
        df_dict = {"Time(s)": didt_times, "dI/dt_raw(A/s)": didt_values}
        
        # 如果原始数据中有分钟时间，也保存下来
        if "Time(min)" in data.columns:
            # 通过插值找到对应的分钟时间
            # 创建一个从秒到分钟的映射函数
            if len(data["Time(s)"]) > 1 and len(data["Time(min)"]) > 1:
                try:
                    sec_to_min = interp1d(
                        data["Time(s)"].values, 
                        data["Time(min)"].values, 
                        bounds_error=False, 
                        fill_value="extrapolate"
                    )
                    # 计算对应的分钟时间
                    min_times = sec_to_min(didt_times)
                    df_dict["Time(min)"] = min_times
                except Exception as e:
                    if cycle_num <= 3:
                        print(f"  Warning: Could not interpolate minute times for cycle {cycle_num}")

        # Convert to pandas dataframe
        df_didt = pd.DataFrame(df_dict)

        # Add smoothed dI/dt using rolling window
        df_didt["dI/dt_smooth(A/s)"] = (
            df_didt["dI/dt_raw(A/s)"].rolling(window=5, center=True).mean()
        )

        # Fill NaN values
        df_didt["dI/dt_smooth(A/s)"] = df_didt["dI/dt_smooth(A/s)"].fillna(
            df_didt["dI/dt_raw(A/s)"]
        )
        
        # 确保数据按时间排序
        df_didt = df_didt.sort_values(by="Time(s)")

        return cycle_num, df_didt
    else:
        if cycle_num <= 10 or cycle_num % 20 == 0:
            print(f"  Warning: No valid dI/dt data for cycle {cycle_num}")
        return cycle_num, None


def process_dIdt_data_parallel(all_cycles_data, text_widget=None, downsample_factor=1):
    """Process dI/dt calculations using parallel processing for better performance"""
    log("Calculating dI/dt using parallel processing...", text_widget)

    # Create a pool of workers
    num_workers = min(multiprocessing.cpu_count(), len(all_cycles_data))
    log(f"Using {num_workers} CPU cores for parallel processing", text_widget)

    # Prepare data for parallel processing
    cycle_data_list = [(cycle, data) for cycle, data in all_cycles_data.items()]

    # Process data in parallel
    didt_cycles = {}
    with multiprocessing.Pool(num_workers) as pool:
        results = list(
            pool.map(
                process_cycle, [(c, d, downsample_factor) for c, d in cycle_data_list]
            )
        )

    # Collect results
    for cycle_num, df_didt in results:
        if df_didt is not None:
            didt_cycles[cycle_num] = df_didt
            log(
                f"  Processed cycle {cycle_num}: calculated {len(df_didt)} dI/dt data points",
                text_widget,
            )
        else:
            log(
                f"  Warning: Unable to calculate dI/dt for cycle {cycle_num}",
                text_widget,
            )

    log(
        f"Parallel dI/dt calculation complete, processed {len(didt_cycles)} cycles",
        text_widget,
    )
    return didt_cycles


def detect_knee_points(cycle_capacities, min_pts=3):
    """Automatically detect knee points in capacity data using curvature analysis"""
    if not cycle_capacities or len(cycle_capacities) < min_pts:
        return []

    try:
        # Sort cycles
        cycles = sorted(cycle_capacities.keys())
        capacities = [cycle_capacities[c] for c in cycles]

        if len(cycles) < 5:
            return []  # Not enough data for reliable detection

        # Convert to numpy arrays for numerical operations
        x = np.array(cycles)
        y = np.array(capacities)

        # Apply Savitzky-Golay filter to smooth the data
        if len(x) >= 5:
            window_size = min(
                len(x) - (len(x) % 2 == 0), 5
            )  # Make sure window size is odd and <= len(x)
            if window_size >= 3:
                y_smooth = savgol_filter(
                    y, window_size, 2
                )  # Window size 5, polynomial order 2
            else:
                y_smooth = y
        else:
            y_smooth = y

        # Calculate first and second derivatives
        dy = np.gradient(y_smooth)
        d2y = np.gradient(dy)

        # Calculate curvature: κ = |y''| / (1 + y'^2)^(3/2)
        curvature = np.abs(d2y) / (1 + dy**2) ** 1.5

        # Find local maxima in curvature above threshold (knee points)
        knee_indices = []
        threshold = 0.1 * np.max(curvature)

        # Skip first and last few points to avoid edge effects
        for i in range(2, len(curvature) - 2):
            if (
                curvature[i] > threshold
                and curvature[i] > curvature[i - 1]
                and curvature[i] > curvature[i + 1]
            ):
                knee_indices.append(i)

        # Limit to top 3 knee points
        if knee_indices:
            # Sort by curvature value (descending)
            knee_indices = sorted(
                knee_indices, key=lambda i: curvature[i], reverse=True
            )
            knee_indices = knee_indices[:3]  # Take top 3

        knee_cycles = [cycles[i] for i in knee_indices]
        return knee_cycles

    except Exception as e:
        log(f"Error in knee point detection: {str(e)}")
        return []


def export_all_cycles_pdf(
    all_cycles_data,
    didt_cycles_data,
    output_folder,
    sample_name,
    text_widget=None,
    cycles_to_include=None,
    page_size=(10, 12),
    dpi=300,
):
    """Export all or selected cycle data to PDF with improved formatting"""
    log(f"Exporting cycle data to PDF...", text_widget)

    # Create output folder (if it doesn't exist)
    os.makedirs(output_folder, exist_ok=True)

    # Get available cycles
    if cycles_to_include:
        available_cycles = [
            c for c in sorted(all_cycles_data.keys()) if c in cycles_to_include
        ]
        pdf_filename = os.path.join(output_folder, f"{sample_name}_selected_cycles.pdf")
    else:
        available_cycles = sorted(list(all_cycles_data.keys()))
        pdf_filename = os.path.join(output_folder, f"{sample_name}_all_cycles.pdf")

    # Calculate axis limits for consistent plots
    max_time = 0
    max_current = 0
    min_didt = 0

    for cycle in available_cycles:
        if cycle in all_cycles_data:
            data = all_cycles_data[cycle]
            if len(data) > 0:
                max_time = max(max_time, data["Time(s)"].max())
                max_current = max(max_current, data["Current(A)"].max())

        if cycle in didt_cycles_data:
            data = didt_cycles_data[cycle]
            if len(data) > 0:
                min_didt = min(min_didt, data["dI/dt_smooth(A/s)"].min())

    # Create PDF file with metadata
    with PdfPages(pdf_filename) as pdf:
        # Set metadata
        d = pdf.infodict()
        d["Title"] = f"Battery Analysis: {sample_name}"
        d["Author"] = "Battery Analysis Tool"
        d["Subject"] = "Cycle Data Analysis"
        d["CreationDate"] = datetime.today()
        d["ModDate"] = datetime.today()

        # Track progress
        total_cycles = len(available_cycles)
        for i, cycle in enumerate(available_cycles):
            # Create new figure with better formatting
            plt.rcParams["font.family"] = "Arial"
            fig, axs = plt.subplots(2, 1, figsize=page_size)
            fig.suptitle(f"{sample_name} - Cycle {cycle}", fontsize=16, y=0.98)

            # Plot current graph
            if cycle in all_cycles_data:
                data = all_cycles_data[cycle]

                # Downsample if too many points (for display)
                if len(data) > 5000:
                    step = len(data) // 5000
                    plot_data = data.iloc[::step]
                else:
                    plot_data = data

                axs[0].plot(
                    plot_data["Time(s)"], plot_data["Current(A)"], "b-", linewidth=1.5
                )

                # Set consistent limits for all plots
                time_limit = min(max_time, data["Time(s)"].max() * 1.2)  # 20% margin
                current_limit = min(
                    max_current * 1.2, data["Current(A)"].max() * 1.5
                )  # 20-50% margin

                axs[0].set_xlim(0, time_limit)
                axs[0].set_ylim(0, current_limit)
                axs[0].set_xlabel("Time (s)", fontsize=12)
                axs[0].set_ylabel("Current (A)", fontsize=12)
                axs[0].set_title(f"Current vs Time", fontsize=14)
                axs[0].grid(True, alpha=0.3)

                # Add reference lines at 25% and 75% of axis limits
                axs[0].axhline(
                    y=current_limit * 0.25, color="black", linestyle="--", alpha=0.5
                )
                axs[0].axhline(
                    y=current_limit * 0.75, color="black", linestyle="--", alpha=0.5
                )
                axs[0].axvline(
                    x=time_limit * 0.15, color="black", linestyle="--", alpha=0.5
                )
                axs[0].axvline(
                    x=time_limit * 0.75, color="black", linestyle="--", alpha=0.5
                )

            # Plot dI/dt graph
            if cycle in didt_cycles_data:
                didt_data = didt_cycles_data[cycle]

                # Downsample if too many points
                if len(didt_data) > 5000:
                    step = len(didt_data) // 5000
                    plot_didt_data = didt_data.iloc[::step]
                else:
                    plot_didt_data = didt_data

                axs[1].plot(
                    plot_didt_data["Time(s)"],
                    plot_didt_data["dI/dt_smooth(A/s)"],
                    "r-",
                    linewidth=1.5,
                )

                # Set consistent limits
                axs[1].set_xlim(0, time_limit)
                didt_limit = max(abs(min_didt * 1.2), 0.001)  # 20% margin
                axs[1].set_ylim(-didt_limit, 0)
                axs[1].set_xlabel("Time (s)", fontsize=12)
                axs[1].set_ylabel("dI/dt (A/s)", fontsize=12)
                axs[1].set_title(f"dI/dt vs Time", fontsize=14)
                axs[1].grid(True, alpha=0.3)

                # Add reference lines
                axs[1].axvline(
                    x=time_limit * 0.15, color="black", linestyle="--", alpha=0.5
                )
                axs[1].axvline(
                    x=time_limit * 0.75, color="black", linestyle="--", alpha=0.5
                )

            # Add cycle information
            cycle_info = (
                f"Cycle: {cycle}/{total_cycles} ({i+1}/{len(available_cycles)})"
            )
            fig.text(0.02, 0.02, cycle_info, fontsize=9)

            # Adjust layout
            plt.tight_layout(rect=[0, 0.03, 1, 0.95])

            # Save to PDF with high DPI
            pdf.savefig(fig, dpi=dpi)
            plt.close(fig)

            if (i + 1) % 10 == 0 or i == len(available_cycles) - 1:
                progress = ((i + 1) / len(available_cycles)) * 100
                log(
                    f"  Progress: {progress:.1f}% ({i+1}/{len(available_cycles)} cycles)",
                    text_widget,
                )

    log(f"PDF export complete: {pdf_filename}", text_widget)
    return pdf_filename


def extract_cycles_from_json(json_data, text_widget=None):
    """Extract cycle data from JSON data structure with improved error handling"""
    log("Extracting cycle data from JSON...", text_widget)

    all_cycles_data = {}
    cycle_capacities = {}
    cycle_ce_values = {}

    # 检查数据格式：支持数组格式（CV数据）和字典格式（原格式）
    if isinstance(json_data, list):
        # CV JSON数据格式（数组）
        log("Detected CV JSON format (array)", text_widget)
        
        for cycle_data in json_data:
            try:
                cycle_num = cycle_data['cycle']
                
                # 提取CV阶段数据
                cv_phase = cycle_data['cv_phase']
                time_s = cv_phase['time']
                current_A = [c/1000 for c in cv_phase['current']]  # mA转A
                
                if len(time_s) > 0 and len(current_A) > 0:
                    # 创建DataFrame
                    df = pd.DataFrame({
                        "Time(s)": time_s,
                        "Current(A)": current_A
                    })
                    
                    all_cycles_data[cycle_num] = df
                    cycle_capacities[cycle_num] = cycle_data['QD'] / 1000  # mAh转Ah
                    cycle_ce_values[cycle_num] = cycle_data['CE']
                    
                    log(f"  Extracted cycle {cycle_num}, containing {len(df)} data points", text_widget)
                    
            except (KeyError, ValueError, TypeError) as e:
                log(f"  Error processing cycle data: {str(e)}", text_widget)
                continue
                
    elif isinstance(json_data, dict):
        # 原始JSON格式（字典）
        log("Detected original JSON format (dictionary)", text_widget)
        
        # 原有的字典处理逻辑
        for key in json_data.keys():
            if key.startswith("Cycle_"):
                try:
                    cycle_num = int(key.split("_")[1])
                    cycle_data = json_data[key]

                    if not isinstance(cycle_data, dict):
                        log(f"  Warning: Invalid data format for cycle {key}", text_widget)
                        continue

                    time_min = cycle_data.get("relative_time_min", [])
                    current_A = cycle_data.get("current_A", [])

                    if not time_min or not current_A:
                        log(f"  Warning: Missing time or current data for cycle {key}", text_widget)
                        continue

                    if len(time_min) != len(current_A):
                        log(f"  Warning: Time and current arrays have different lengths in cycle {key}", text_widget)
                        min_len = min(len(time_min), len(current_A))
                        time_min = time_min[:min_len]
                        current_A = current_A[:min_len]

                    if len(time_min) > 0 and len(current_A) > 0:
                        start_time = time_min[0]
                        time_s = [(t - start_time) * 60 for t in time_min]

                        df = pd.DataFrame({"Time(s)": time_s, "Current(A)": current_A})
                        df["Time(min)"] = [t - start_time for t in time_min]

                        all_cycles_data[cycle_num] = df
                        log(f"  Extracted cycle {cycle_num}, containing {len(df)} data points", text_widget)
                        
                except (ValueError, IndexError, TypeError) as e:
                    log(f"  Error processing cycle {key}: {str(e)}", text_widget)
                    continue

        # 处理CE数据（原格式）
        if "CE" in json_data:
            try:
                ce_values = json_data["CE"]
                if isinstance(ce_values, list):
                    for i, ce in enumerate(ce_values):
                        cycle_num = i + 1
                        if cycle_num in all_cycles_data:
                            try:
                                ce_value = float(ce)
                                cycle_ce_values[cycle_num] = ce_value
                                log(f"  Extracted CE for cycle {cycle_num}: {ce_value:.6f}", text_widget)
                            except (ValueError, TypeError):
                                log(f"  Warning: Non-numeric CE value for cycle {cycle_num}", text_widget)
                else:
                    log(f"  Warning: 'CE' is not a list", text_widget)
            except Exception as e:
                log(f"  Error processing CE data: {str(e)}", text_widget)
    else:
        log("Error: Invalid JSON data format - expected a dictionary or array", text_widget)
        return None, None, None

    log(f"Data extraction complete, {len(all_cycles_data)} cycles found", text_widget)
    return all_cycles_data, cycle_capacities, cycle_ce_values


def extract_cycles_from_csv(csv_data, text_widget=None):
    """Extract cycle data from CSV data with enhanced validation and error handling"""
    log("Extracting cycle data from CSV...", text_widget)

    all_cycles_data = {}
    cycle_capacities = {}

    # Check if required columns exist
    required_columns = ["Time(s)", "Current(A)"]
    for col in required_columns:
        if col not in csv_data.columns:
            log(f"Error: Required column '{col}' missing in data file", text_widget)
            return None, None

    # Verify data types and convert if needed
    try:
        csv_data["Time(s)"] = pd.to_numeric(csv_data["Time(s)"], errors="coerce")
        csv_data["Current(A)"] = pd.to_numeric(csv_data["Current(A)"], errors="coerce")

        # Check for NaN values after conversion
        nan_times = csv_data["Time(s)"].isna().sum()
        nan_currents = csv_data["Current(A)"].isna().sum()

        if nan_times > 0:
            log(
                f"Warning: Found {nan_times} non-numeric time values (will be dropped)",
                text_widget,
            )
        if nan_currents > 0:
            log(
                f"Warning: Found {nan_currents} non-numeric current values (will be dropped)",
                text_widget,
            )

        # Drop rows with NaN values
        csv_data = csv_data.dropna(subset=["Time(s)", "Current(A)"]).reset_index(
            drop=True
        )

    except Exception as e:
        log(f"Error converting data columns: {str(e)}", text_widget)
        return None, None

    # Check if Cycle column exists
    if "Cycle" in csv_data.columns:
        try:
            # Convert Cycle column to numeric if it's not
            csv_data["Cycle"] = pd.to_numeric(csv_data["Cycle"], errors="coerce")

            # Check for invalid cycle numbers
            invalid_cycles = csv_data["Cycle"].isna().sum()
            if invalid_cycles > 0:
                log(
                    f"Warning: Found {invalid_cycles} rows with invalid cycle numbers",
                    text_widget,
                )
                csv_data = csv_data.dropna(subset=["Cycle"]).reset_index(drop=True)

            # Convert cycle numbers to integers
            csv_data["Cycle"] = csv_data["Cycle"].astype(int)

            # Group data by cycle
            for cycle, group in csv_data.groupby("Cycle"):
                # Create a copy of the group data
                cycle_data = group.copy()

                # Normalize time to start from 0 for each cycle
                if len(cycle_data) > 0:
                    min_time = cycle_data["Time(s)"].min()
                    cycle_data["Time(s)"] = cycle_data["Time(s)"] - min_time

                all_cycles_data[cycle] = cycle_data

                # Calculate capacity for this cycle (Ah)
                # Integrate current over time: capacity = ∫I·dt
                capacity = 0
                time_values = cycle_data["Time(s)"].values
                current_values = cycle_data["Current(A)"].values

                for i in range(1, len(time_values)):
                    dt = (
                        time_values[i] - time_values[i - 1]
                    ) / 3600  # convert seconds to hours
                    avg_current = (
                        current_values[i] + current_values[i - 1]
                    ) / 2  # average current
                    capacity += avg_current * dt

                # Store capacity for this cycle
                cycle_capacities[cycle] = abs(capacity)

                log(
                    f"  Extracted cycle {cycle}, containing {len(cycle_data)} data points, capacity: {abs(capacity):.4f} Ah",
                    text_widget,
                )
        except Exception as e:
            log(f"Error processing cycle data: {str(e)}", text_widget)
            log(traceback.format_exc(), text_widget)
            return None, None
    else:
        # If no Cycle column, treat all data as one cycle
        cycle_data = csv_data.copy()

        # Normalize time to start from 0
        if len(cycle_data) > 0:
            min_time = cycle_data["Time(s)"].min()
            cycle_data["Time(s)"] = cycle_data["Time(s)"] - min_time

        all_cycles_data[1] = cycle_data

        # Calculate capacity
        capacity = 0
        time_values = cycle_data["Time(s)"].values
        current_values = cycle_data["Current(A)"].values

        for i in range(1, len(time_values)):
            dt = (
                time_values[i] - time_values[i - 1]
            ) / 3600  # convert seconds to hours
            avg_current = (
                current_values[i] + current_values[i - 1]
            ) / 2  # average current
            capacity += avg_current * dt

        # Store capacity
        cycle_capacities[1] = abs(capacity)

        log(
            f"  Data file has no Cycle column, treating all {len(csv_data)} data points as cycle 1, capacity: {abs(capacity):.4f} Ah",
            text_widget,
        )

    log(f"Data extraction complete, {len(all_cycles_data)} cycles found", text_widget)
    return all_cycles_data, cycle_capacities


class PlotManager:
    """Class to handle plotting and visualization functionality"""

    def __init__(self):
        self.plots = {}
        self.marked_points = {}
        self.custom_annotations = {}
        self.default_colors = plt.rcParams["axes.prop_cycle"].by_key()["color"]
        self.color_map = cm.get_cmap("tab10")

    def create_figure(self, figsize=(10, 12), dpi=100):
        """Create a new figure with appropriate subplots"""
        fig = plt.figure(figsize=figsize, dpi=dpi)

        # Create grid for plots (2x2)
        gs = fig.add_gridspec(2, 2, hspace=0.25, wspace=0.3)

        # Create the four subplots
        ax_current = fig.add_subplot(gs[0, 0])  # Current vs Time
        ax_didt = fig.add_subplot(gs[0, 1])  # dI/dt vs Time
        ax_capacity = fig.add_subplot(gs[1, 0])  # Capacity vs Cycle
        ax_knee = fig.add_subplot(gs[1, 1])  # Knee point analysis

        # Set titles and labels
        ax_current.set_title("Current vs Time")
        ax_current.set_xlabel("Time (s)")
        ax_current.set_ylabel("Current (A)")

        ax_didt.set_title("dI/dt vs Time")
        ax_didt.set_xlabel("Time (s)")
        ax_didt.set_ylabel("dI/dt (A/s)")

        ax_capacity.set_title("Capacity vs Cycle")
        ax_capacity.set_xlabel("Cycle Number")
        ax_capacity.set_ylabel("Capacity (Ah)")

        ax_knee.set_title("Knee Point Analysis")
        ax_knee.set_xlabel("Cycle Number")
        ax_knee.set_ylabel("Value")

        # Enable grid
        for ax in [ax_current, ax_didt, ax_capacity, ax_knee]:
            ax.grid(True, alpha=0.3)

        axes = {
            "current": ax_current,
            "didt": ax_didt,
            "capacity": ax_capacity,
            "knee": ax_knee,
        }

        return fig, axes

    def plot_cycle_current(self, ax, cycle_data, cycle_num, color=None, label=None):
        """Plot current vs time for a cycle"""
        if color is None:
            color = self.color_map(cycle_num % 10)

        if label is None:
            label = f"Cycle {cycle_num}"

        # Downsample if too many points
        if len(cycle_data) > 5000:
            step = len(cycle_data) // 5000
            plot_data = cycle_data.iloc[::step]
        else:
            plot_data = cycle_data

        (line,) = ax.plot(
            plot_data["Time(s)"],
            plot_data["Current(A)"],
            "-",
            color=color,
            label=label,
            linewidth=1.5,
        )

        return line

    def plot_cycle_didt(
        self, ax, didt_data, cycle_num, color=None, linestyle="-", alpha=0.7, zorder=5
    ):
        """Plot dI/dt vs time for a cycle (支持下采样、线型、透明度、zorder)"""
        if didt_data is None or len(didt_data) == 0:
            return None
        if color is None:
            color = self.color_map(cycle_num % 10)

        # 下采样到1000点以内，但保证关键点不被过滤
        if len(didt_data) > 1000:
            # 使用更智能的下采样方法，保留曲线形状
            # 1. 按时间排序
            sorted_data = didt_data.sort_values(by="Time(s)")

            # 2. 计算总时间范围
            time_range = sorted_data["Time(s)"].max() - sorted_data["Time(s)"].min()

            # 3. 分段下采样，确保各时间段都有足够的点
            segments = 20  # 将时间范围分为20段
            segment_size = time_range / segments
            plot_data = []

            for i in range(segments):
                start_time = sorted_data["Time(s)"].min() + i * segment_size
                end_time = start_time + segment_size
                segment_data = sorted_data[
                    (sorted_data["Time(s)"] >= start_time)
                    & (sorted_data["Time(s)"] < end_time)
                ]

                # 每段最多取50个点
                if len(segment_data) > 50:
                    indices = np.linspace(0, len(segment_data) - 1, 50, dtype=int)
                    segment_data = segment_data.iloc[indices]

                plot_data.append(segment_data)

            # 合并所有分段
            plot_data = pd.concat(plot_data)
        else:
            plot_data = didt_data
        
        # 绘制
        if "dI/dt_smooth(A/s)" in plot_data.columns:
            # 确保使用秒为单位的时间
            time_values = plot_data["Time(s)"]
            didt_values = plot_data["dI/dt_smooth(A/s)"]

            # 过滤掉异常值，提高可视化效果
            valid_mask = (~np.isnan(didt_values)) & (~np.isinf(didt_values))
            if not all(valid_mask):
                time_values = time_values[valid_mask]
                didt_values = didt_values[valid_mask]

            (line,) = ax.plot(
                time_values,
                didt_values,
                linestyle=linestyle,
                linewidth=1.5,
                color=color,
                alpha=alpha,
                zorder=zorder,
            )

            # 设置坐标轴标签，明确显示单位
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("dI/dt (A/s)")
            return line
        elif "dI/dt_raw(A/s)" in plot_data.columns:
            # 确保使用秒为单位的时间
            time_values = plot_data["Time(s)"]
            didt_values = plot_data["dI/dt_raw(A/s)"]

            # 过滤掉异常值，提高可视化效果
            valid_mask = (~np.isnan(didt_values)) & (~np.isinf(didt_values))
            if not all(valid_mask):
                time_values = time_values[valid_mask]
                didt_values = didt_values[valid_mask]

            (line,) = ax.plot(
                time_values,
                didt_values,
                linestyle=linestyle,
                linewidth=1.5,
                color=color,
                alpha=alpha,
                zorder=zorder,
            )

            # 设置坐标轴标签，明确显示单位
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("dI/dt (A/s)")
            return line
        else:
            print(f"Warning: No dI/dt column found in data for cycle {cycle_num}")
            return None

    def plot_capacities(
        self, ax, cycle_capacities, knee_points=None, point_colors=None
    ):
        """Plot capacity vs cycle number with optional knee points"""
        cycles = sorted(cycle_capacities.keys())
        capacities = [cycle_capacities[c] for c in cycles]

        # Plot capacity curve
        (line,) = ax.plot(cycles, capacities, "o-", markersize=3, linewidth=1.5)

        # Add trend line (moving average)
        if len(cycles) > 5:
            window = min(5, len(cycles))
            trend = pd.Series(capacities).rolling(window=window, center=True).mean()
            ax.plot(cycles, trend, "r--", linewidth=1.5, alpha=0.7, label="Trend")

        # Mark knee points if provided
        if knee_points:
            knee_capacities = [cycle_capacities.get(cycle, 0) for cycle in knee_points]
            ax.plot(
                knee_points,
                knee_capacities,
                "rx",
                markersize=10,
                markeredgewidth=2,
                label="Knee Points",
            )

        ax.set_xlim(min(cycles) - 1 if cycles else 0, max(cycles) + 1 if cycles else 10)
        if capacities:
            y_min = min(capacities) * 0.9
            y_max = max(capacities) * 1.1
            ax.set_ylim(y_min, y_max)

        ax.legend(loc="best")
        return line

    def mark_point(self, ax, x, y, cycle_num, marker="o", size=10, color=None):
        """Mark a point on the plot"""
        if color is None:
            color = self.color_map(cycle_num % 10)

        point = ax.plot(x, y, marker, markersize=size, color=color)[0]

        # Store point reference
        if cycle_num not in self.marked_points:
            self.marked_points[cycle_num] = []
        self.marked_points[cycle_num].append((point, ax, x, y))

        return point

    def add_annotation(self, ax, x, y, text, cycle_num, color=None):
        """Add text annotation to a plot"""
        if color is None:
            color = self.color_map(cycle_num % 10)

        annotation = ax.annotate(
            text,
            (x, y),
            xytext=(5, 5),
                               textcoords="offset points", 
            color=color,
            fontsize=9,
        )

        # Store annotation reference
        if cycle_num not in self.custom_annotations:
            self.custom_annotations[cycle_num] = []
        self.custom_annotations[cycle_num].append(annotation)

        return annotation

    def clear_markings(self, cycle_num=None):
        """Clear marked points and annotations for specific cycle or all cycles"""
        if cycle_num is not None:
            # Clear specific cycle markings
            if cycle_num in self.marked_points:
                for point, _, _, _ in self.marked_points[cycle_num]:
                    point.remove()
                del self.marked_points[cycle_num]

            if cycle_num in self.custom_annotations:
                for annotation in self.custom_annotations[cycle_num]:
                    annotation.remove()
                del self.custom_annotations[cycle_num]
        else:
            # Clear all markings
            for cycle in list(self.marked_points.keys()):
                for point, _, _, _ in self.marked_points[cycle]:
                    point.remove()
            self.marked_points = {}

            for cycle in list(self.custom_annotations.keys()):
                for annotation in self.custom_annotations[cycle]:
                    annotation.remove()
            self.custom_annotations = {}

    def update_plot(self, fig):
        """Update the figure canvas"""
        
        fig.canvas.draw_idle()


class BatteryAnalysisApp:
    """Main application class for the battery analysis tool"""

    def __init__(self, root):
        self.root = root
        self.root.title("Battery Analysis Tool")
        self.root.geometry("1200x800")

        # Initialize data storage
        self.all_cycles_data = {}
        self.didt_cycles_data = {}
        self.cycle_capacities = {}
        self.cycle_ce_values = {}  # Add CE values storage
        self.sample_name = "Sample"
        self.marked_knee_points = []
        self.auto_detected_knee_points = []

        # Initialize plot manager
        self.plot_manager = PlotManager()

        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create file input frame
        self.create_file_input_frame()

        # Create log output frame
        self.create_log_frame()

        # Set up drag and drop
        self.setup_drag_drop()

        # Set up keyboard shortcuts
        self.setup_keyboard_shortcuts()

    def create_file_input_frame(self):
        """Create the file input section of the GUI"""
        input_frame = ttk.LabelFrame(self.main_frame, text="File Input")
        input_frame.pack(fill=tk.X, pady=10)

        # File selection row
        file_row = ttk.Frame(input_frame)
        file_row.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(file_row, text="Data File:").pack(side=tk.LEFT, padx=(0, 10))

        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_row, textvariable=self.file_path_var, width=50)
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        browse_button = ttk.Button(file_row, text="Browse", command=self.browse_file)
        browse_button.pack(side=tk.LEFT)

        # Sample name row
        name_row = ttk.Frame(input_frame)
        name_row.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(name_row, text="Sample Name:").pack(side=tk.LEFT, padx=(0, 10))

        self.sample_name_var = tk.StringVar(value="Sample")
        sample_entry = ttk.Entry(name_row, textvariable=self.sample_name_var, width=30)
        sample_entry.pack(side=tk.LEFT, padx=(0, 10))

        # Process button row
        button_row = ttk.Frame(input_frame)
        button_row.pack(fill=tk.X, padx=10, pady=10)

        process_button = ttk.Button(
            button_row, text="Process File", command=self.process_file
        )
        process_button.pack(side=tk.LEFT, padx=(0, 10))

        self.progress_var = tk.DoubleVar(value=0)
        self.progress_bar = ttk.Progressbar(
            button_row, variable=self.progress_var, maximum=100
        )
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Advanced options
        self.create_advanced_options(input_frame)

    def create_advanced_options(self, parent):
        """Create advanced options section"""
        options_frame = ttk.LabelFrame(parent, text="Advanced Options")
        options_frame.pack(fill=tk.X, padx=10, pady=10)

        # Options grid
        grid = ttk.Frame(options_frame)
        grid.pack(fill=tk.X, padx=10, pady=10)

        # Row 1: Visualization options
        ttk.Label(grid, text="dI/dt Internal:").grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=5
        )

        self.didt_internal_var = tk.IntVar(value=50)
        didt_internal_spin = ttk.Spinbox(
            grid, from_=5, to=200, width=10, textvariable=self.didt_internal_var
        )
        didt_internal_spin.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(grid, text="Downsample Factor:").grid(
            row=0, column=2, sticky=tk.W, padx=5, pady=5
        )

        self.downsample_var = tk.IntVar(value=1)
        downsample_spin = ttk.Spinbox(
            grid, from_=1, to=50, width=10, textvariable=self.downsample_var
        )
        downsample_spin.grid(row=0, column=3, padx=5, pady=5)

        # Row 2: Processing options
        ttk.Label(grid, text="Use Multiprocessing:").grid(
            row=1, column=0, sticky=tk.W, padx=5, pady=5
        )

        self.use_multiprocessing_var = tk.BooleanVar(value=True)
        multiprocessing_check = ttk.Checkbutton(
            grid, variable=self.use_multiprocessing_var
        )
        multiprocessing_check.grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(grid, text="Auto-detect Knees:").grid(
            row=1, column=2, sticky=tk.W, padx=5, pady=5
        )

        self.auto_detect_knees_var = tk.BooleanVar(value=True)
        auto_detect_check = ttk.Checkbutton(grid, variable=self.auto_detect_knees_var)
        auto_detect_check.grid(row=1, column=3, padx=5, pady=5)

        # Row 3: Batch processing
        ttk.Label(grid, text="Batch Processing:").grid(
            row=2, column=0, sticky=tk.W, padx=5, pady=5
        )

        batch_button = ttk.Button(
            grid, text="Select Files...", command=self.select_batch_files
        )
        batch_button.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)

        export_all_button = ttk.Button(
            grid, text="Export All Data", command=self.export_all_data
        )
        export_all_button.grid(
            row=2, column=2, columnspan=2, padx=5, pady=5, sticky=tk.W
        )

    def create_log_frame(self):
        """Create the log output section of the GUI"""
        log_frame = ttk.LabelFrame(self.main_frame, text="Log Output")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create scrolled text widget for log output
        self.log_text = tk.Text(log_frame, height=10, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # Disable text editing
        self.log_text.config(state=tk.DISABLED)

        # Add version info to log
        self.log("Battery Analysis Tool v2.0")
        self.log(
            "Ready to process files. Drag and drop files or use the Browse button."
        )

    def setup_drag_drop(self):
        """Set up drag and drop functionality for files"""
        # Try different drag-drop libraries - use whichever is available
        try:
            # Try with TkinterDnD2
            self.root.tk.call("package", "require", "tkdnd")
            self.root.tk.call("package", "require", "TkinterDnD2")
            from TkinterDnD2 import TkinterDnD, DND_FILES

            self.root = (
                TkinterDnD.Tk() if not isinstance(self.root, tk.Tk) else self.root
            )
            self.file_entry.drop_target_register(DND_FILES)
            self.file_entry.dnd_bind("<<Drop>>", self.handle_drop)
            self.log("Drag and drop enabled (TkinterDnD2)")
        except Exception as e:
            try:
                # Alternative: try with tkdnd
                self.root.tk.eval("package require tkdnd")
                self.file_entry.drop_target_register("DND_Files")
                self.file_entry.dnd_bind("<<Drop>>", self.handle_drop)
                self.log("Drag and drop enabled (tkdnd)")
            except Exception as e:
                self.log("Drag and drop not available - missing tkdnd package")

                # Setup basic file drop for systems with no tkdnd
                def paste_from_clipboard(event):
                    try:
                        clipboard = self.root.clipboard_get()
                        if os.path.isfile(clipboard):
                            self.file_path_var.set(clipboard)
                    except:
                        pass

                self.file_entry.bind(
                    "<Button-3>", paste_from_clipboard
                )  # Right-click to paste

    def setup_keyboard_shortcuts(self):
        """Set up keyboard shortcuts for common operations"""
        self.root.bind("<Control-o>", lambda e: self.browse_file())
        self.root.bind("<Control-p>", lambda e: self.process_file())
        self.root.bind("<Control-e>", lambda e: self.export_all_data())
        self.root.bind("<Control-h>", lambda e: self.show_help())
        self.root.bind("<Escape>", lambda e: self.root.focus_set())

        # Display shortcut help in log
        self.log(
            "Keyboard shortcuts: Ctrl+O (open), Ctrl+P (process), Ctrl+E (export), Ctrl+H (help)"
        )

    def show_help(self):
        """Show help information"""
        help_text = """
Battery Analysis Tool Help

File Input:
- Drag and drop files onto the entry field
- Use Browse button or Ctrl+O to select file
- Supported formats: CSV, JSON

Processing:
- Click "Process File" or press Ctrl+P
- Use advanced options to configure processing

Visualization:
- Interactive cycle viewer opens after processing
- Click and drag on plots to select data points
- Use sliders to select cycles

Export:
- Export current view: Use buttons in viewer
- Export all data: Click "Export All Data" or press Ctrl+E

Keyboard Shortcuts:
- Ctrl+O: Open file
- Ctrl+P: Process file
- Ctrl+E: Export all data
- Ctrl+H: Show this help
- Escape: Clear focus

For more information, see the documentation.
"""
        messagebox.showinfo("Battery Analysis Tool Help", help_text)

    def log(self, message):
        """Add message to the log widget"""
        log(message, self.log_text)

    def browse_file(self):
        """Open file dialog to select data file"""
        file_path = filedialog.askopenfilename(
            title="Select Data File",
            filetypes=(
                ("CSV Files", "*.csv"),
                ("JSON Files", "*.json"),
                ("All Files", "*.*"),
            ),
        )

        if file_path:
            self.file_path_var.set(file_path)
            # Auto-set sample name from filename
            base_name = os.path.basename(file_path)
            sample_name = os.path.splitext(base_name)[0]
            self.sample_name_var.set(sample_name)
            self.log(f"Selected file: {file_path}")

    def handle_drop(self, event):
        """Handle file drop event"""
        file_path = event.data

        # Clean up path (remove {} and leading/trailing spaces)
        if file_path.startswith("{") and file_path.endswith("}"):
            file_path = file_path[1:-1]
        file_path = file_path.strip()

        if os.path.isfile(file_path):
            self.file_path_var.set(file_path)
            # Auto-set sample name from filename
            base_name = os.path.basename(file_path)
            sample_name = os.path.splitext(base_name)[0]
            self.sample_name_var.set(sample_name)
            self.log(f"Dropped file: {file_path}")
        else:
            self.log(f"Error: Invalid file path from drop - {file_path}")

    def select_batch_files(self):
        """Select multiple files for batch processing"""
        file_paths = filedialog.askopenfilenames(
            title="Select Data Files for Batch Processing",
            filetypes=(
                ("CSV Files", "*.csv"),
                ("JSON Files", "*.json"),
                ("All Files", "*.*"),
            ),
        )

        if file_paths:
            self.log(f"Selected {len(file_paths)} files for batch processing")
            self.batch_process_files(file_paths)

    def batch_process_files(self, file_paths):
        """Process multiple files in batch mode"""
        if not file_paths:
            return

        # Ask for output directory
        output_dir = filedialog.askdirectory(
            title="Select Output Directory for Batch Results"
        )
        if not output_dir:
            return

        # Create progress window
        progress_window = tk.Toplevel(self.root)
        progress_window.title("Batch Processing")
        progress_window.geometry("400x150")

        ttk.Label(progress_window, text="Processing files...").pack(pady=10)

        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            progress_window, variable=progress_var, maximum=len(file_paths)
        )
        progress_bar.pack(fill=tk.X, padx=20, pady=10)

        status_var = tk.StringVar(value="Initializing...")
        status_label = ttk.Label(progress_window, textvariable=status_var)
        status_label.pack(pady=10)

        # Process files in a separate thread
        def run_batch_processing():
            results = []

            for i, file_path in enumerate(file_paths):
                try:
                    # Update status
                    file_name = os.path.basename(file_path)
                    self.root.after(
                        0,
                        lambda: status_var.set(
                            f"Processing {file_name} ({i+1}/{len(file_paths)})"
                        ),
                    )
                    self.root.after(0, lambda v=i + 1: progress_var.set(v))

                    # Extract sample name
                    sample_name = os.path.splitext(file_name)[0]

                    # Process file
                    result = self.process_file_internal(
                        file_path, sample_name, batch_mode=True
                    )
                    if result:
                        results.append((sample_name, result))

                        # Export results
                        self.export_data_internal(result, output_dir, sample_name)

                except Exception as e:
                    self.log(f"Error processing {file_name}: {str(e)}")

            # Complete
            self.root.after(
                0,
                lambda: status_var.set(
                    f"Complete! Processed {len(results)}/{len(file_paths)} files"
                ),
            )
            self.root.after(
                0,
                lambda: self.log(
                    f"Batch processing complete: {len(results)}/{len(file_paths)} files processed"
                ),
            )

            # Enable close button after processing
            self.root.after(
                0,
                lambda: ttk.Button(
                    progress_window, text="Close", command=progress_window.destroy
                ).pack(pady=10),
            )

        threading.Thread(target=run_batch_processing, daemon=True).start()


    def process_file(self):
        """Process the selected file"""
        file_path = self.file_path_var.get()
        if not file_path:
            self.log("Error: No file selected")
            return

        if not os.path.isfile(file_path):
            self.log(f"Error: File not found - {file_path}")
            return

        sample_name = self.sample_name_var.get()
        if not sample_name:
            sample_name = "Sample"

        # Update progress bar
        self.progress_var.set(0)

        # Process in a separate thread
        def run_processing():
            result = self.process_file_internal(file_path, sample_name)
            if result:
                (
                    self.all_cycles_data,
                    self.didt_cycles_data,
                    self.cycle_capacities,
                    self.cycle_ce_values,
                ) = result
                self.sample_name = sample_name

                # Update progress
                self.root.after(0, lambda: self.progress_var.set(100))

                # Run visualization
                self.root.after(0, self.visualize_interactive)

        threading.Thread(target=run_processing, daemon=True).start()

    def process_file_internal(self, file_path, sample_name, batch_mode=False):
        """Internal function for file processing logic"""
        try:
            self.log(f"Processing file: {file_path}")
            self.log(f"Sample name: {sample_name}")

            # Update progress
            if not batch_mode:
                self.root.after(0, lambda: self.progress_var.set(10))

            # Check file extension
            ext = os.path.splitext(file_path)[1].lower()

            # Load data based on file type
            if ext == ".csv":
                self.log("Detected CSV file, loading data...")
                try:
                    data = pd.read_csv(file_path)
                    self.log(f"Loaded {len(data)} rows from CSV file")
                    all_cycles_data, cycle_capacities = extract_cycles_from_csv(
                        data, self.log_text
                    )
                    cycle_ce_values = {}  # CSV files don't have CE data by default
                except Exception as e:
                    self.log(f"Error loading CSV file: {str(e)}")
                    return None

            elif ext == ".json":
                self.log("Detected JSON file, loading data...")
                try:
                    with open(file_path, "r") as f:
                        data = json.load(f)
                    self.log(f"Loaded JSON file with {len(data)} elements")
                    all_cycles_data, cycle_capacities, cycle_ce_values = (
                        extract_cycles_from_json(data, self.log_text)
                    )
                except Exception as e:
                    self.log(f"Error loading JSON file: {str(e)}")
                    return None
            else:
                self.log(f"Error: Unsupported file extension: {ext}")
                return None

            # Check if data was extracted
            if not all_cycles_data:
                self.log("Error: No cycle data extracted from file")
                return None

            # Update progress
            if not batch_mode:
                self.root.after(0, lambda: self.progress_var.set(40))

            # Process dI/dt data
            didt_internal = self.didt_internal_var.get()
            downsample_factor = self.downsample_var.get()
            use_multiprocessing = self.use_multiprocessing_var.get()

            didt_cycles_data = process_dIdt_data(
                all_cycles_data,
                self.log_text,
                downsample_factor=downsample_factor,
                use_multiprocessing=use_multiprocessing,
            )

            # Check if dI/dt data was calculated
            if not didt_cycles_data:
                self.log("Warning: No dI/dt data calculated")

            # Update progress
            if not batch_mode:
                self.root.after(0, lambda: self.progress_var.set(80))

            # Check if we should auto-detect knee points
            if self.auto_detect_knees_var.get() and cycle_capacities:
                self.auto_detected_knee_points = detect_knee_points(cycle_capacities)
                if self.auto_detected_knee_points:
                    self.log(
                        f"Auto-detected knee points at cycles: {', '.join(map(str, self.auto_detected_knee_points))}"
                    )
                else:
                    self.log("No knee points auto-detected")
            else:
                self.auto_detected_knee_points = []

            # Complete progress
            if not batch_mode:
                self.root.after(0, lambda: self.progress_var.set(100))

            self.log(
                f"Processing complete: {len(all_cycles_data)} cycles, {len(didt_cycles_data)} dI/dt data sets"
            )
            return all_cycles_data, didt_cycles_data, cycle_capacities, cycle_ce_values

        except Exception as e:
            self.log(f"Error processing file: {str(e)}")
            self.log(traceback.format_exc())
            return None

        """Internal function for file processing logic"""
        try:
            self.log(f"Processing file: {file_path}")
            self.log(f"Sample name: {sample_name}")

            # Update progress
            if not batch_mode:
                self.root.after(0, lambda: self.progress_var.set(10))

            # Check file extension
            ext = os.path.splitext(file_path)[1].lower()

            # Load data based on file type
            if ext == ".csv":
                self.log("Detected CSV file, loading data...")
                try:
                    data = pd.read_csv(file_path)
                    self.log(f"Loaded {len(data)} rows from CSV file")
                    all_cycles_data, cycle_capacities = extract_cycles_from_csv(
                        data, self.log_text
                    )
                except Exception as e:
                    self.log(f"Error loading CSV file: {str(e)}")
                    return None

            elif ext == ".json":
                self.log("Detected JSON file, loading data...")
                try:
                    with open(file_path, "r") as f:
                        data = json.load(f)
                    self.log(f"Loaded JSON file with {len(data)} elements")
                    all_cycles_data, cycle_capacities = extract_cycles_from_json(
                        data, self.log_text
                    )
                except Exception as e:
                    self.log(f"Error loading JSON file: {str(e)}")
                    return None
            else:
                self.log(f"Error: Unsupported file extension: {ext}")
                return None

            # Check if data was extracted
            if not all_cycles_data:
                self.log("Error: No cycle data extracted from file")
                return None

            # Update progress
            if not batch_mode:
                self.root.after(0, lambda: self.progress_var.set(40))

            # Process dI/dt data
            didt_internal = self.didt_internal_var.get()
            downsample_factor = self.downsample_var.get()
            use_multiprocessing = self.use_multiprocessing_var.get()

            didt_cycles_data = process_dIdt_data(
                all_cycles_data, 
                self.log_text,
                downsample_factor=downsample_factor,
                use_multiprocessing=use_multiprocessing,
            )

            # Check if dI/dt data was calculated
            if not didt_cycles_data:
                self.log("Warning: No dI/dt data calculated")

            # Update progress
            if not batch_mode:
                self.root.after(0, lambda: self.progress_var.set(80))

            # Check if we should auto-detect knee points
            if self.auto_detect_knees_var.get() and cycle_capacities:
                self.auto_detected_knee_points = detect_knee_points(cycle_capacities)
                if self.auto_detected_knee_points:
                    self.log(
                        f"Auto-detected knee points at cycles: {', '.join(map(str, self.auto_detected_knee_points))}"
                    )
                else:
                    self.log("No knee points auto-detected")
            else:
                self.auto_detected_knee_points = []

            # Complete progress
            if not batch_mode:
                self.root.after(0, lambda: self.progress_var.set(100))

            self.log(
                f"Processing complete: {len(all_cycles_data)} cycles, {len(didt_cycles_data)} dI/dt data sets"
            )
            return all_cycles_data, didt_cycles_data, cycle_capacities

        except Exception as e:
            self.log(f"Error processing file: {str(e)}")
            self.log(traceback.format_exc())
            return None

    def visualize_interactive(self):
        """Launch interactive visualization window"""
        self.log("Launching interactive visualization...")
        
        try:
            # Launch visualization in a separate thread to keep UI responsive
            def run_visualization():
                try:
                    interactive_cycle_viewer(
                        self.all_cycles_data,
                        self.didt_cycles_data,
                        self.cycle_capacities,
                        self.sample_name,
                        cycle_ce_values=self.cycle_ce_values,  # Pass CE values
                        knee_points=self.auto_detected_knee_points,
                    )
                except Exception as e:
                    self.log(f"Error in visualization: {str(e)}")
                    self.log(traceback.format_exc())
                    
            threading.Thread(target=run_visualization, daemon=True).start()
            
        except Exception as e:
            self.log(f"Error launching visualization: {str(e)}")


    def export_all_data(self):
        """Export all processed data"""
        if not self.all_cycles_data or not self.cycle_capacities:
            self.log("No data to export")
            messagebox.showinfo("Export", "No data to export. Process a file first.")
            return

        # Ask for output directory
        output_dir = filedialog.askdirectory(title="Select Output Directory")
        if not output_dir:
            return

        # Call internal export function
        self.export_data_internal(
            (self.all_cycles_data, self.didt_cycles_data, self.cycle_capacities),
            output_dir,
            self.sample_name,
        )

    def export_data_internal(self, data_tuple, output_dir, sample_name):
        """Internal function for data export logic"""
        try:
            all_cycles_data, didt_cycles_data, cycle_capacities = data_tuple

            self.log(f"Exporting data to {output_dir}...")

            # Create directory for this sample
            sample_dir = os.path.join(output_dir, sample_name)
            os.makedirs(sample_dir, exist_ok=True)

            # Export cycle data to CSV
            cycles_csv_path = os.path.join(sample_dir, f"{sample_name}_cycles.csv")
            with open(cycles_csv_path, "w") as f:
                f.write("Cycle,Capacity(Ah)\n")
                for cycle, capacity in sorted(cycle_capacities.items()):
                    f.write(f"{cycle},{capacity:.6f}\n")

            self.log(f"Exported cycle capacities to {cycles_csv_path}")

            # Export PDF with all cycles
            pdf_path = export_all_cycles_pdf(
                all_cycles_data,
                didt_cycles_data,
                sample_dir,
                sample_name,
                self.log_text,
            )

            self.log(f"Exported cycle plots to {pdf_path}")

            # Export summary data as JSON
            summary_data = {
                "sample_name": sample_name,
                "total_cycles": len(cycle_capacities),
                "capacity_data": {
                    str(c): float(cap) for c, cap in cycle_capacities.items()
                },
                "auto_detected_knee_points": [
                    int(k) for k in self.auto_detected_knee_points
                ],
                "marked_knee_points": [int(k) for k in self.marked_knee_points],
                "export_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }

            json_path = os.path.join(sample_dir, f"{sample_name}_summary.json")
            with open(json_path, "w") as f:
                json.dump(summary_data, f, indent=2)

            self.log(f"Exported summary data to {json_path}")

            # Export individual cycle data
            cycles_dir = os.path.join(sample_dir, "cycles")
            os.makedirs(cycles_dir, exist_ok=True)

            for cycle in sorted(all_cycles_data.keys())[
                :10
            ]:  # Export first 10 cycles individually
                cycle_csv_path = os.path.join(
                    cycles_dir, f"{sample_name}_cycle_{cycle}.csv"
                )
                all_cycles_data[cycle].to_csv(cycle_csv_path, index=False)

            self.log(f"Exported individual cycle data to {cycles_dir}")

            return True

        except Exception as e:
            self.log(f"Error exporting data: {str(e)}")
            self.log(traceback.format_exc())
            return False


def interactive_cycle_viewer(
    all_cycles_data, didt_cycles_data, cycle_capacities, sample_name, 
    cycle_ce_values=None, knee_points=None
):
    """Interactive visualization for cycle data with improved structure"""
    # Create a new window for the interactive viewer
    viewer = tk.Toplevel()
    viewer.title(f"Interactive Cycle Viewer - {sample_name}")
    viewer.geometry("1200x900")

    # Create a PlotManager instance
    plot_manager = PlotManager()

    # Get available cycles
    available_cycles = sorted(list(all_cycles_data.keys()))
    if not available_cycles:
        messagebox.showerror("Error", "No cycle data available for visualization")
        viewer.destroy()
        return

    # Calculate min/max cycle
    min_cycle = min(available_cycles)
    max_cycle = max(available_cycles)

    # Create the figure and axes
    fig, axes = plot_manager.create_figure(figsize=(10, 8))

    # Create main frame
    main_frame = ttk.Frame(viewer)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # Create frame for the figure canvas
    canvas_frame = ttk.Frame(main_frame)
    canvas_frame.pack(fill=tk.BOTH, expand=True)

    # Create canvas for the figure
    canvas = FigureCanvasTkAgg(fig, canvas_frame)
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    # Create navigation toolbar
    toolbar = NavigationToolbar2Tk(canvas, canvas_frame)
    toolbar.update()

    # Create control frame
    control_frame = ttk.Frame(main_frame)
    control_frame.pack(fill=tk.X, padx=10, pady=5)

    # Variables for slider values
    cycle_var = tk.IntVar(value=min_cycle)

    # Create twin axis for CE data
    ax_ce = axes["capacity"].twinx()
    ax_ce.set_ylabel("Coulombic Efficiency (CE)")
    ax_ce.set_ylim(0.98, 1.01)  # Fixed range for CE as specified

    # Calculate appropriate y-axis limits for dI/dt plot
    min_didt = 0
    for c in available_cycles:
        if (
            c in didt_cycles_data
            and didt_cycles_data[c] is not None
            and len(didt_cycles_data[c]) > 0
        ):
            if "dI/dt_smooth(A/s)" in didt_cycles_data[c].columns:
                min_val = didt_cycles_data[c]["dI/dt_smooth(A/s)"].min()
                min_didt = min(min_didt, min_val)
            elif "dI/dt_raw(A/s)" in didt_cycles_data[c].columns:
                min_val = didt_cycles_data[c]["dI/dt_raw(A/s)"].min()
                min_didt = min(min_didt, min_val)

    # Set y-axis limits
    didt_limit = max(abs(min_didt * 1.2), 0.001)  # 20% margin
    axes["didt"].set_ylim(-didt_limit, 0)

    def update_plots(*args):
        # Get current cycle
        cycle = cycle_var.get()

        # Clear previous plots
        for ax in axes.values():
            ax.clear()

        # Clear the CE axis as well
        ax_ce.clear()
        ax_ce.set_ylabel("Coulombic Efficiency (CE)")
        ax_ce.set_ylim(0.98, 1.01)  # Fixed range for CE

        # Reset plot titles and labels
        axes["current"].set_title("Current vs Time")
        axes["current"].set_xlabel("Time (s)")
        axes["current"].set_ylabel("Current (A)")

        axes["didt"].set_title("dI/dt vs Time")
        axes["didt"].set_xlabel("Time (s)")
        axes["didt"].set_ylabel("dI/dt (A/s)")

        axes["capacity"].set_title("Capacity vs Cycle")
        axes["capacity"].set_xlabel("Cycle Number")
        axes["capacity"].set_ylabel("Capacity (Ah)")

        axes["knee"].set_title("Cycle Analysis")
        axes["knee"].set_xlabel("Cycle Number")
        axes["knee"].set_ylabel("Value")

        # Plot current data for all cycles
        import matplotlib.cm as cm
        import numpy as np

        n_cycles = len(available_cycles)
        # 使用viridis渐变色
        color_map = cm.get_cmap("viridis", n_cycles)
        colors = [color_map(i) for i in range(n_cycles)]

        current_time_min = None
        current_time_max = None
        for i, c in enumerate(available_cycles):
            if c in all_cycles_data:
                color = colors[i]
                plot_manager.plot_cycle_current(
                    axes["current"], all_cycles_data[c], c, color=color
                )
                # 记录时间范围
                tmin = all_cycles_data[c]["Time(s)"].min()
                tmax = all_cycles_data[c]["Time(s)"].max()
                if current_time_min is None or tmin < current_time_min:
                    current_time_min = tmin
                if current_time_max is None or tmax > current_time_max:
                    current_time_max = tmax

        # Highlight the selected cycle with thicker line
        if cycle in all_cycles_data:
            plot_manager.plot_cycle_current(
                axes["current"], all_cycles_data[cycle], cycle, color="black"
            )

        # Plot dI/dt data for all cycles - 使用与current相同的颜色方案
        all_didt_min = 0
        all_didt_max = 0

        for i, c in enumerate(available_cycles):
            if (
                c in didt_cycles_data
                and didt_cycles_data[c] is not None
                and len(didt_cycles_data[c]) > 0
            ):
                color = colors[i]  # 使用与current相同的颜色
                line = plot_manager.plot_cycle_didt(
                    axes["didt"],
                    didt_cycles_data[c],
                    c,
                    color=color,
                    linestyle="-",
                    alpha=0.7,
                    zorder=5,
                )

                # 收集所有循环的dI/dt最大最小值，用于后续设置y轴范围
                if line is not None:
                    if "dI/dt_smooth(A/s)" in didt_cycles_data[c].columns:
                        didt_min = didt_cycles_data[c]["dI/dt_smooth(A/s)"].min()
                        didt_max = didt_cycles_data[c]["dI/dt_smooth(A/s)"].max()
                    elif "dI/dt_raw(A/s)" in didt_cycles_data[c].columns:
                        didt_min = didt_cycles_data[c]["dI/dt_raw(A/s)"].min()
                        didt_max = didt_cycles_data[c]["dI/dt_raw(A/s)"].max()

                    all_didt_min = min(all_didt_min, didt_min)
                    all_didt_max = max(all_didt_max, didt_max)

        # Highlight the selected cycle with thicker line
        if (
            cycle in didt_cycles_data
            and didt_cycles_data[cycle] is not None
            and len(didt_cycles_data[cycle]) > 0
        ):
            plot_manager.plot_cycle_didt(
                axes["didt"],
                didt_cycles_data[cycle],
                cycle,
                color="black",
                linestyle="-",
                alpha=1.0,
                zorder=10,
            )

        # 设置dI/dt图的x轴范围与current一致
        if current_time_min is not None and current_time_max is not None:
            axes["didt"].set_xlim(current_time_min, current_time_max)

        # 设置dI/dt图的y轴范围，确保所有曲线都可见
        if all_didt_min != 0 or all_didt_max != 0:
            # 添加10%的边距
            y_margin = max(abs(all_didt_max - all_didt_min) * 0.1, 0.001)
            axes["didt"].set_ylim(all_didt_min - y_margin, all_didt_max + y_margin)
        else:
            # 如果没有有效数据，设置默认范围
            axes["didt"].set_ylim(-0.05, 0.01)

        # Plot capacity data
        plot_manager.plot_capacities(
            axes["capacity"], cycle_capacities, knee_points, point_colors=colors
        )

        # Plot CE data if available
        if cycle_ce_values and len(cycle_ce_values) > 0:
            ce_cycles = sorted(list(cycle_ce_values.keys()))
            ce_values = [cycle_ce_values[c] for c in ce_cycles]

            # Plot CE data with scatter plot
            ax_ce.scatter(ce_cycles, ce_values, color='red', marker='o', s=20, 
                         alpha=0.7, label='CE')

            # Add connecting lines
            ax_ce.plot(ce_cycles, ce_values, 'r-', alpha=0.4)

            # Highlight selected cycle
            if cycle in cycle_ce_values:
                ax_ce.scatter([cycle], [cycle_ce_values[cycle]], color='black', 
                             marker='s', s=50)

            # Set CE axis limits and add legend
            ax_ce.set_ylim(0.98, 1.01)
            ax_ce.legend(loc='upper right')

        # Mark selected cycle in capacity plot
        if cycle in cycle_capacities:
            axes["capacity"].plot(
                [cycle], [cycle_capacities[cycle]], "go", markersize=10
            )

        # Plot cycle analysis data
        cycles = sorted(list(cycle_capacities.keys()))
        capacities = [cycle_capacities[c] for c in cycles]

        # Plot capacity derivative (rate of capacity change)
        if len(cycles) > 3:
            try:
                capacity_array = np.array(capacities)
                cycle_array = np.array(cycles)

                # Calculate capacity change rate using rolling window
                window_size = min(5, len(cycles) - 1)
                if window_size >= 2:
                    capacity_diff = np.diff(capacity_array)
                    cycle_diff = np.diff(cycle_array)
                    change_rate = capacity_diff / cycle_diff

                    # Plot rate of change (smoothed)
                    if len(change_rate) > window_size:
                        smoothed_rate = (
                            pd.Series(change_rate)
                            .rolling(window=window_size, center=True)
                            .mean()
                            .fillna(method="bfill")
                            .fillna(method="ffill")
                        )
                        axes["knee"].plot(
                            cycles[1:],
                            smoothed_rate,
                            "b-",
                            label="Capacity Change Rate",
                        )
                    else:
                        axes["knee"].plot(
                            cycles[1:], change_rate, "b-", label="Capacity Change Rate"
                        )

                    # Plot reference line at zero
                    axes["knee"].axhline(y=0, color="r", linestyle="--", alpha=0.5)

                # Mark knee points if available
                if knee_points:
                    knee_y_values = [0 for _ in knee_points]  # Place markers at y=0
                    axes["knee"].plot(
                        knee_points,
                        knee_y_values,
                        "rx",
                        markersize=10,
                        markeredgewidth=2,
                        label="Knee Points",
                    )

                axes["knee"].legend(loc="best")
            except Exception as e:
                print(f"Error plotting cycle analysis: {str(e)}")

        # Enable grid on all plots
        for ax in axes.values():
            ax.grid(True, alpha=0.3)

        # Add grid to CE axis as well
        ax_ce.grid(False)  # Disable grid on CE axis to avoid cluttering

        # Update the figure
        plot_manager.update_plot(fig)

    # Create sliders frame
    sliders_frame = ttk.LabelFrame(control_frame, text="Controls")
    sliders_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

    # Create cycle slider
    cycle_slider_frame = ttk.Frame(sliders_frame)
    cycle_slider_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(cycle_slider_frame, text="Cycle:").pack(side=tk.LEFT)
    cycle_slider = ttk.Scale(
        cycle_slider_frame,
        from_=min_cycle,
        to=max_cycle,
        orient=tk.HORIZONTAL,
        variable=cycle_var,
        command=lambda *args: update_plots(),
    )
    cycle_slider.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)

    cycle_spinner = ttk.Spinbox(
        cycle_slider_frame,
        from_=min_cycle,
        to=max_cycle,
        textvariable=cycle_var,
        width=5,
        command=lambda: update_plots(),
    )
    cycle_spinner.pack(side=tk.LEFT)

    # Create buttons frame
    buttons_frame = ttk.LabelFrame(control_frame, text="Actions")
    buttons_frame.pack(side=tk.LEFT, fill=tk.X, padx=(10, 0))

    # Button to mark knee point
    def mark_knee_point():
        cycle = cycle_var.get()
        if cycle in cycle_capacities:
            if cycle not in knee_points:
                knee_points.append(cycle)
                knee_points.sort()
                messagebox.showinfo(
                    "Knee Point", f"Marked cycle {cycle} as a knee point"
                )
                update_plots()

    mark_knee_button = ttk.Button(
        buttons_frame, text="Mark as Knee Point", command=mark_knee_point
    )
    mark_knee_button.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)

    # Button to clear knee points
    def clear_knee_points():
        nonlocal knee_points
        if knee_points:
            knee_points = []
            messagebox.showinfo("Knee Points", "Cleared all knee points")
            update_plots()

    clear_knee_button = ttk.Button(
        buttons_frame, text="Clear Knee Points", command=clear_knee_points
    )
    clear_knee_button.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)

    # Export buttons
    export_frame = ttk.LabelFrame(control_frame, text="Export")
    export_frame.pack(side=tk.LEFT, fill=tk.X, padx=(10, 0))

    # Function to export current view
    def export_current_view():
        file_path = filedialog.asksaveasfilename(
            title="Save Current View",
            defaultextension=".png",
            filetypes=(
                ("PNG Image", "*.png"),
                ("PDF Document", "*.pdf"),
                ("SVG Image", "*.svg"),
            ),
        )

        if file_path:
            ext = os.path.splitext(file_path)[1].lower()

            # Different export methods based on file type
            if ext == ".pdf":
                fig.savefig(file_path, format="pdf", dpi=300)
            elif ext == ".svg":
                fig.savefig(file_path, format="svg")
            else:  # Default to PNG
                fig.savefig(file_path, format="png", dpi=300)

            messagebox.showinfo("Export", f"Current view exported to {file_path}")

    export_view_button = ttk.Button(
        export_frame, text="Export Current View", command=export_current_view
    )
    export_view_button.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)

    # Function to export data for current cycle
    def export_cycle_data():
        cycle = cycle_var.get()

        file_path = filedialog.asksaveasfilename(
            title=f"Save Cycle {cycle} Data",
            defaultextension=".csv",
            filetypes=(("CSV File", "*.csv"), ("Excel File", "*.xlsx")),
        )

        if file_path:
            ext = os.path.splitext(file_path)[1].lower()

            try:
                # Get cycle data
                data = all_cycles_data.get(cycle)
                didt_data = didt_cycles_data.get(cycle)

                if data is None:
                    messagebox.showerror(
                        "Export Error", f"No data available for cycle {cycle}"
                    )
                    return

                # Create a combined dataframe
                export_data = data.copy()

                # Add dI/dt data if available
                if didt_data is not None:
                    # Merge on Time column
                    didt_subset = didt_data[["Time(s)", "dI/dt_smooth(A/s)"]]
                    export_data = pd.merge_asof(
                        export_data.sort_values("Time(s)"),
                        didt_subset.sort_values("Time(s)"),
                        on="Time(s)",
                        direction="nearest",
                    )

                # Add cycle number column
                export_data["Cycle"] = cycle

                # Add capacity if available
                if cycle in cycle_capacities:
                    export_data["Capacity(Ah)"] = cycle_capacities[cycle]

                # Add CE if available
                if cycle in cycle_ce_values:
                    export_data["CE"] = cycle_ce_values[cycle]

                # Export based on file type
                if ext == ".xlsx":
                    export_data.to_excel(file_path, index=False)
                else:  # Default to CSV
                    export_data.to_csv(file_path, index=False)

                messagebox.showinfo(
                    "Export", f"Cycle {cycle} data exported to {file_path}"
                )

            except Exception as e:
                messagebox.showerror("Export Error", f"Error exporting data: {str(e)}")

    export_data_button = ttk.Button(
        export_frame, text="Export Cycle Data", command=export_cycle_data
    )
    export_data_button.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)

    # Information display frame
    info_frame = ttk.LabelFrame(control_frame, text="Information")
    info_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=(10, 0))

    # Create status labels
    cycle_info_var = tk.StringVar()
    capacity_info_var = tk.StringVar()
    ce_info_var = tk.StringVar()  # New variable for CE information

    ttk.Label(info_frame, textvariable=cycle_info_var).pack(anchor=tk.W, padx=5, pady=2)
    ttk.Label(info_frame, textvariable=capacity_info_var).pack(
        anchor=tk.W, padx=5, pady=2
    )
    ttk.Label(info_frame, textvariable=ce_info_var).pack(
        anchor=tk.W, padx=5, pady=2
    )

    # Function to update information display
    def update_info():
        cycle = cycle_var.get()
        cycle_info = f"Cycle: {cycle}/{max_cycle}"

        capacity = cycle_capacities.get(cycle, 0)
        capacity_info = f"Capacity: {capacity:.4f} Ah"

        # Add CE information if available
        if cycle_ce_values and cycle in cycle_ce_values:
            ce = cycle_ce_values[cycle]
            ce_info = f"CE: {ce:.6f}"
        else:
            ce_info = "CE: N/A"

        cycle_info_var.set(cycle_info)
        capacity_info_var.set(capacity_info)
        ce_info_var.set(ce_info)

    # Add observer to update info when cycle changes
    cycle_var.trace_add("write", lambda *args: update_info())

    # Register mouse click handler to select data points
    def on_click(event):
        if event.inaxes is None:
            return

        if event.inaxes == axes["capacity"] or event.inaxes == ax_ce:
            # Find closest cycle point
            if not cycle_capacities:
                return

            cycles = sorted(list(cycle_capacities.keys()))
            capacities = [cycle_capacities[c] for c in cycles]

            # Convert from data to display coordinates
            x_disp, y_disp = event.inaxes.transData.transform(
                (event.xdata, event.ydata)
            )

            # Find closest point
            min_dist = float("inf")
            closest_cycle = None

            for i, c in enumerate(cycles):
                x, y = cycles[i], capacities[i]
                x_d, y_d = event.inaxes.transData.transform((x, y))
                dist = np.sqrt((x_d - x_disp) ** 2 + (y_d - y_disp) ** 2)

                if dist < min_dist:
                    min_dist = dist
                    closest_cycle = c

            # If close enough to a point, select that cycle
            if min_dist < 50 and closest_cycle is not None:  # 50 pixels threshold
                cycle_var.set(closest_cycle)
                update_plots()

    # Connect click event
    fig.canvas.mpl_connect("button_press_event", on_click)

    # Set up keyboard shortcuts for navigation
    def on_key(event):
        cycle = cycle_var.get()

        if event.key == "right" or event.key == "n":
            # Move to next cycle
            next_cycle = min(cycle + 1, max_cycle)
            cycle_var.set(next_cycle)
            update_plots()
        elif event.key == "left" or event.key == "p":
            # Move to previous cycle
            prev_cycle = max(cycle - 1, min_cycle)
            cycle_var.set(prev_cycle)
            update_plots()
        elif event.key == "home":
            # Move to first cycle
            cycle_var.set(min_cycle)
            update_plots()
        elif event.key == "end":
            # Move to last cycle
            cycle_var.set(max_cycle)
            update_plots()
        elif event.key == "k":
            # Mark as knee point
            mark_knee_point()
        elif event.key == "s":
            # Export current view
            export_current_view()

    # Connect key event
    fig.canvas.mpl_connect("key_press_event", on_key)

    # Add help text to bottom of window
    help_text = (
        "Keyboard: ← → to navigate cycles | k: mark knee point | s: save view | "
        "Click on capacity plot to select cycle"
    )
    help_label = ttk.Label(main_frame, text=help_text, foreground="gray")
    help_label.pack(side=tk.BOTTOM, pady=(0, 5))

    # Initial plot
    update_plots()
    update_info()

    # Set window to be resizable
    viewer.update_idletasks()
    viewer.minsize(viewer.winfo_width(), viewer.winfo_height())
    viewer.deiconify()

    # Configure the grid to be resizable
    main_frame.columnconfigure(0, weight=1)
    main_frame.rowconfigure(0, weight=1)

    # Handle window resize event
    def on_resize(event=None):
        if event and event.widget == viewer:
            fig.set_size_inches(
                event.width / fig.dpi,
                (event.height - control_frame.winfo_height() - 30) / fig.dpi,
            )
            canvas.draw_idle()

    viewer.bind("<Configure>", on_resize)

    # Handle window close event
    def on_close():
        plt.close(fig)
        viewer.destroy()

    viewer.protocol("WM_DELETE_WINDOW", on_close)

    # Start the event loop
    viewer.mainloop()


def main():
    """Main entry point for the application"""
    try:
        root = tk.Tk()
        root.title("Battery Analysis Tool")

        # Set icon if available
        try:
            # Try to use a built-in icon
            root.iconbitmap(default="battery.ico")
        except:
            pass

        # Create and run application
        app = BatteryAnalysisApp(root)

        # Center window on screen
        window_width = 1000
        window_height = 800
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = int((screen_width - window_width) / 2)
        y = int((screen_height - window_height) / 2)
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Start the application
        root.mainloop()

    except Exception as e:
        print(f"Error starting application: {str(e)}")
        traceback.print_exc()

        # Try to show error in GUI if possible
        try:
            messagebox.showerror(
                "Application Error", f"Error starting application: {str(e)}"
            )
        except:
            pass


if __name__ == "__main__":
    main()









